<template>
  <div class="home-container">
    <div class="home-content">
      <!-- 左侧内容 -->
      <div class="home-left">
        <div class="logo-section">
          <AppLogo size="large" variant="colored" text="RecipeHub" :showText="true" />
        </div>

        <h2 class="main-title">Cook with</h2>
        <p class="description">探索食谱，组织您的膳食，简化购物流程。</p>

        <a-button type="primary" size="large" class="start-button" @click="handleGetStarted">
          开始使用
        </a-button>
      </div>

      <!-- 右侧图片 -->
      <div class="home-right">
        <div class="image-container">
          <img
            src="https://picsum.photos/seed/recipe1/600/600"
            alt="Cooking illustration with recipe books and ingredients"
            class="hero-image"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useLoginUserStore } from '@/stores/userLoginStore'
import { AppLogo } from '@/components'

/**
 * 首页组件
 * 对应HTML原型中的index.html
 */

const router = useRouter()
const userStore = useLoginUserStore()

const handleGetStarted = () => {
  // 如果已登录，跳转到仪表板；否则跳转到登录页面
  if (userStore.loginUser?.userRole && userStore.loginUser.userRole !== 'notLogin') {
    router.push('/dashboard')
  } else {
    router.push('/user/login')
  }
}
</script>

<style lang="less" scoped>
.home-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.home-content {
  width: 100%;
  max-width: 1200px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 48px;
  align-items: center;
}

.home-left {
  background: #fff;
  border-radius: 24px;
  padding: 48px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);

  .logo-section {
    margin-bottom: 32px;
  }

  .main-title {
    font-size: clamp(2rem, 5vw, 3.5rem);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 16px;
    color: #1a1a1a;
  }

  .description {
    font-size: 18px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 32px;
  }

  .start-button {
    height: 48px;
    padding: 0 32px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 12px;
    background: #ff7a00;
    border-color: #ff7a00;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      background: rgba(255, 122, 0, 0.9);
      border-color: rgba(255, 122, 0, 0.9);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(255, 122, 0, 0.3);
    }
  }
}

.home-right {
  .image-container {
    background: #fff;
    border-radius: 24px;
    padding: 48px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;

    .hero-image {
      max-width: 100%;
      height: auto;
      border-radius: 12px;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.02);
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .home-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .home-left,
  .home-right .image-container {
    padding: 32px 24px;
  }

  .home-left .main-title {
    font-size: 2rem;
  }
}
</style>
