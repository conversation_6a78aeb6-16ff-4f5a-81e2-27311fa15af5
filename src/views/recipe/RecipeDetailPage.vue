<template>
  <div class="recipe-detail">
    <a-row :gutter="[24, 0]">
      <!-- 左侧图片区域 -->
      <a-col :span="12" class="image-column">
        <div class="recipe-image-container">
          <a-image
            :src="recipe?.coverKey ? defaultCoverUrl : defaultCoverUrl"
            class="recipe-image"
          />
        </div>
      </a-col>

      <!-- 右侧内容区域 -->
      <a-col :span="12" class="recipe-content">
        <div class="recipe-content-scrollable">
          <div class="recipe-header">
            <div class="recipe-meta">
              <span class="recipe-category">{{ recipe?.category }}</span>
              <span class="recipe-tag">{{ recipe?.tags }}</span>
            </div>
            <h1 class="recipe-title">{{ recipe?.name }}</h1>

            <!-- <div class="recipe-rating">
              <a-rate :default-value="5" disabled />
              <span class="review-count">120 Reviews</span>
            </div> -->
          </div>

          <p class="recipe-description">
            {{ recipe?.introduction }}
          </p>
          <div class="segmented-container">
            <a-segmented
              v-model:value="switchFlag"
              block
              :options="options"
              @change="handleSegmentedChange"
            />
            <div class="seperate"></div>
            <IngredientList
              v-if="switchFlag === 'IngredientList'"
              :ingredientList="customIngredients"
            />
            <div
              v-if="switchFlag === 'RecipeStepList'"
              class="recipe-step-preview-container"
              style="width: 100%; height: 250px; min-height: 250px"
            >
              <!-- 加载状态 -->
              <div v-if="recipeStepStore.isLoadingFromServer" class="loading-container">
                <a-spin size="large" tip="正在获取最新菜谱数据..." />
              </div>

              <!-- 错误状态 -->
              <div
                v-else-if="recipeStepStore.loadError && !recipeStepStore.hasData"
                class="error-container"
              >
                <a-result
                  status="error"
                  :title="recipeStepStore.loadError"
                  sub-title="请检查网络连接后重试"
                >
                  <template #extra>
                    <a-button type="primary" @click="fecthRecipe">重新加载</a-button>
                  </template>
                </a-result>
              </div>

              <!-- 正常流程图 -->
              <RecipeStepFlow
                v-else
                :recipeList="recipeStepList"
                mode="preview"
                height="250px"
                :showToolbar="false"
                :editable="false"
                @nodeClick="handlePreviewNodeClick"
                style="width: 100%; height: 100%"
              />
            </div>

            <div class="action-buttons-container">
              <a-button class="action-button action-button--edit" @click="handleEditClick">
                编辑<EditOutlined />
              </a-button>
              <a-button
                class="action-button action-button--details"
                @click="handleViewDetailsClick"
              >
                查看详情<EllipsisOutlined />
              </a-button>
            </div>
          </div>
        </div>
      </a-col>
    </a-row>

    <div class="pop-window">
      <!-- 添加食材详情弹窗 -->
      <IngredientDetailModal
        :visible="modalVisible"
        :mode="modalMode"
        :ingredients="customIngredients"
        :editable="modalMode === 'edit'"
        @close="handleModalClose"
        @save="handleIngredientsSave"
      />

      <RecipeStepDetail
        :visible="recipeStepVisible"
        :mode="recipeStepMode"
        :recipeStepList="recipeStepList"
        :editable="recipeStepMode === 'edit'"
        @close="handleModalClose"
        @save="handleRecipeStepSave"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, watch, reactive, ref, computed } from 'vue'
import { Row, Col, Button, Rate, message, Table } from 'ant-design-vue'
import { useRoute, useRouter } from 'vue-router'
import { get } from 'lodash-es'
import defaultCoverUrlPath from '@/assest/images/DefaultImage.jpg'
import IngredientList from '@/components/recipe/IngredientList.vue'
import { EditOutlined, EllipsisOutlined } from '@ant-design/icons-vue'
import IngredientDetailModal from '@/components/recipe/IngredientDetailModal.vue'
import RecipeStepDetail from '@/views/recipe/RecipeStepDetail.vue'
import RecipeStepFlow from '@/components/recipe/RecipeStepFlow.vue'
import { useRecipeStepStore } from '@/stores/recipeStepStore'

const router = useRouter()
const route = useRoute()
const recipeStepStore = useRecipeStepStore()
const options = [
  { label: '食材清单', value: 'IngredientList' },
  { label: '详细步骤', value: 'RecipeStepList' },
]
const recipe = ref<any>()
const switchFlag = ref(options[0].value)
const defaultCoverUrl = ref<string>(defaultCoverUrlPath)
const customIngredients = ref([
  { key: '1', name: '高筋面粉', amount: '250g' },
  { key: '2', name: '酵母', amount: '5g' },
  { key: '3', name: '盐', amount: '3g' },
  { key: '4', name: '温水', amount: '150ml' },
  { key: '5', name: '橄榄油', amount: '10ml' },
  { key: '6', name: '高筋面粉', amount: '250g' },
  { key: '7', name: '酵母', amount: '5g' },
  { key: '8', name: '盐', amount: '3g' },
  { key: '9', name: '温水', amount: '150ml' },
  { key: '10', name: '橄榄油', amount: '10ml' },
])
const recipeId = Array.isArray(route.params?.id) ? route.params.id[0] : route.params?.id || ''

//步骤弹窗
// 添加弹窗相关的响应式数据
const recipeStepVisible = ref(false)
const recipeStepMode = ref<'edit' | 'view'>('view')

// 使用计算属性从 Store 中获取步骤列表，确保数据同步
const recipeStepList = computed(() => {
  return recipeStepStore.hasData ? [...recipeStepStore.recipeSteps] : []
})

const handleRecipeStepSave = (updatedStepList: any[]) => {
  // 这里可以调用API保存到后端
  console.log('保存的步骤数据:', updatedStepList)
  // 数据已经通过 Store 管理，不需要额外处理
}

//原材料弹窗
// 添加弹窗相关的响应式数据
const modalVisible = ref(false)
const modalMode = ref<'edit' | 'view'>('view')

// 添加按钮点击事件处理
const handleEditClick = () => {
  if (switchFlag.value == 'IngredientList') {
    modalMode.value = 'edit'
    modalVisible.value = true
  } else {
    recipeStepMode.value = 'edit'
    recipeStepVisible.value = true
  }
}

const handleViewDetailsClick = () => {
  if (switchFlag.value == 'IngredientList') {
    modalMode.value = 'view'
    modalVisible.value = true
  } else {
    recipeStepMode.value = 'view'
    recipeStepVisible.value = true
  }
}

const handleModalClose = () => {
  if (switchFlag.value == 'IngredientList') {
    modalVisible.value = false
  } else {
    recipeStepVisible.value = false
  }
}

const handleIngredientsSave = (ingredients: any[]) => {
  // 保存食材数据的逻辑
  customIngredients.value = ingredients
  modalVisible.value = false
  // 这里可以调用API保存到后端
  console.log('保存的食材数据:', ingredients)
}
const handleSegmentedChange = (value: string) => {
  switchFlag.value = value
  console.log('switch flag', switchFlag.value)
}

// 预览节点点击事件
const handlePreviewNodeClick = (node: any) => {
  console.log('Preview node clicked:', node)
  // 可以在这里添加预览节点点击的逻辑，比如高亮显示等
}
//根据id查询菜谱 - 使用Store的强制刷新模式
const fecthRecipe = async () => {
  try {
    console.log('🔄 开始从服务器获取菜谱数据...')

    // 使用Store的loadRecipeFromServer方法，实现强制刷新
    const result = await recipeStepStore.loadRecipeFromServer(recipeId)

    if (result.success) {
      // 成功获取数据
      recipe.value = result.data
      console.log('✅ 菜谱数据加载成功:', result.data)
    } else {
      // 加载失败
      if (result.fallbackToLocal) {
        // 使用了本地缓存数据
        message.warning('网络连接失败，使用本地缓存数据')
        console.log('⚠️ 使用本地缓存数据')
      } else {
        // 完全失败
        message.error(result.error || '获取菜谱数据失败')
        console.error('❌ 获取菜谱数据失败:', result.error)
      }
    }
  } catch (error) {
    console.error('❌ 获取菜谱数据异常:', error)
    message.error('获取菜谱数据异常')
  }
}

onMounted(() => {
  console.log('📍 进入菜谱详情页面，菜谱ID:', recipeId)
  fecthRecipe()

  console.log('🔍 当前Store状态:', recipeStepStore.getCurrentInfo())
})

// 监听路由参数变化，如果菜谱ID改变，重新加载数据
watch(
  () => route.params.id,
  (newId, oldId) => {
    if (newId !== oldId && newId) {
      console.log(`🔄 菜谱ID变化: ${oldId} → ${newId}`)
      fecthRecipe()
    }
  },
  { immediate: false },
)

// 离开页面时清空Store中的数据
onBeforeUnmount(() => {
  console.log('📤 离开菜谱详情页面，清空Store数据')
  console.log('🔍 清空前Store状态:', recipeStepStore.getCurrentInfo())
  recipeStepStore.clearCurrentData()
  console.log('🔍 清空后Store状态:', recipeStepStore.getCurrentInfo())
})
</script>

<style scoped>
.recipe-detail {
  padding: 32px;
  max-width: 1200px;
  margin: 24px auto;
}

.image-column {
  display: flex;
  align-items: center;
  justify-content: center;
}

.recipe-image-container {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  transition: transform 0.3s ease;
}

.recipe-image-container:hover {
  transform: translateY(-5px);
}

.recipe-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.5s ease;
}

.recipe-image-container:hover .recipe-image {
  transform: scale(1.03);
}

.recipe-content {
  padding-left: 32px;
  height: 600px; /* 固定高度 */
  overflow: hidden;
}

.recipe-content-scrollable {
  height: 100%;
  overflow-y: auto;
  padding-right: 8px;
  display: flex;
  flex-direction: column;
}

.recipe-content-scrollable::-webkit-scrollbar {
  width: 6px;
}

.recipe-content-scrollable::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.recipe-content-scrollable::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.recipe-content-scrollable::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.recipe-header {
  margin-bottom: 24px;
}

.recipe-meta {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
}

.recipe-category {
  font-size: 14px;
  color: #ff7a00;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
  padding: 4px 12px;
  background-color: rgba(255, 122, 0, 0.1);
  border-radius: 20px;
}

.recipe-tag {
  font-size: 14px;
  color: #666;
  padding: 4px 12px;
  background-color: #f5f5f5;
  border-radius: 20px;
}

.recipe-title {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 16px;
  color: #222;
  line-height: 1.2;
}

.recipe-rating {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.ant-rate {
  margin-right: 12px;
  color: #ffb400;
}

.review-count {
  color: #888;
  font-size: 14px;
}

.recipe-description {
  color: #555;
  line-height: 1.8;
  margin-bottom: 24px;
  font-size: 16px;
  padding-right: 16px;
}

.recipe-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 32px;
}

.serving-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.serves-text {
  color: #666;
  margin: 0;
  font-size: 15px;
}

.servings-control {
  display: flex;
  align-items: center;
}

.quantity-btn {
  width: 36px;
  height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 6px;
  transition: all 0.2s ease;
  font-size: 18px;
}

.quantity-btn:hover {
  background-color: #f5f5f5;
  border-color: #ccc;
}

.quantity {
  padding: 0 20px;
  font-size: 18px;
  font-weight: 500;
}

.price-container {
  position: relative;
}

.price {
  font-size: 32px;
  font-weight: 700;
  color: #222;
  position: relative;
}

.price::before {
  content: '';
  position: absolute;
  bottom: 6px;
  left: 0;
  width: 100%;
  height: 8px;
  background-color: rgba(255, 122, 0, 0.15);
  z-index: -1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .recipe-detail {
    padding: 16px;
    margin: 16px;
  }

  .recipe-title {
    font-size: 32px;
  }

  .recipe-content {
    padding-left: 0;
    margin-top: 24px;
    height: 500px; /* 移动端稍微降低高度 */
  }

  .section-title {
    font-size: 18px;
  }

  .ingredients-table :deep(.ant-table-thead > tr > th),
  .ingredients-table :deep(.ant-table-tbody > tr > td) {
    padding: 8px 12px;
    font-size: 14px;
  }

  .recipe-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}

.seperate {
  margin-top: 16px;
  margin-bottom: 16px;
  border: #ddd 0.5px solid;
}

/* Action Buttons Container */
.action-buttons-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 20%; /* Creates the 20% gap between buttons */
}

/* Base Action Button Styles */
.action-button {
  width: 40%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  border: 2px solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

/* Edit Button - Left Aligned Orange */
.action-button--edit {
  background: #ff7a00;
  border-color: #ff7a00;
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(255, 122, 0, 0.25);
}

.action-button--edit:hover {
  background: #e66d00;
  border-color: #e66d00;
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 122, 0, 0.35);
}

.action-button--edit:active {
  background: #cc5f00;
  border-color: #cc5f00;
  transform: translateY(0);
  box-shadow: 0 4px 12px rgba(255, 122, 0, 0.25);
}

.action-button--edit:focus {
  background: #ff7a00;
  border-color: #ff7a00;
  color: #ffffff;
  box-shadow: 0 0 0 3px rgba(255, 122, 0, 0.2);
}

/* View Details Button - Right Aligned Orange Outline */
.action-button--details {
  background: #ffffff;
  border-color: #ff7a00;
  color: #ff7a00;
  box-shadow: 0 2px 8px rgba(255, 122, 0, 0.1);
}

.action-button--details:hover {
  background: #ff7a00;
  border-color: #ff7a00;
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 122, 0, 0.25);
}

.action-button--details:active {
  background: #e66d00;
  border-color: #e66d00;
  color: #ffffff;
  transform: translateY(0);
  box-shadow: 0 4px 12px rgba(255, 122, 0, 0.25);
}

.action-button--details:focus {
  background: #ffffff;
  border-color: #ff7a00;
  color: #ff7a00;
  box-shadow: 0 0 0 3px rgba(255, 122, 0, 0.2);
}

/* Icon Styling */
.action-button .anticon {
  font-size: 18px;
  transition: transform 0.2s ease;
}

.action-button:hover .anticon {
  transform: scale(1.1);
}

/* Responsive Design for Action Buttons */
@media (max-width: 768px) {
  .action-buttons-container {
    flex-direction: column;
    gap: 16px;
  }

  .action-button {
    width: 100%;
    height: 44px;
    font-size: 15px;
  }

  .action-button .anticon {
    font-size: 16px;
  }
}

.pop-window {
  border: #222;
}

/* 预览容器样式 */
.recipe-step-preview-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  height: 250px;
  min-height: 250px;
}

/* 加载状态容器 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #fafafa;
  border-radius: 8px;
}

/* 错误状态容器 */
.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #fafafa;
  border-radius: 8px;
}

.preview-hint {
  background: linear-gradient(135deg, #fff7ed 0%, #ffedd5 100%);
  border: 1px solid #fed7aa;
  border-radius: 8px;
  padding: 12px 16px;
  text-align: center;
  margin-top: 8px;
}

.hint-text {
  margin: 0;
  color: #9a3412;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.hint-icon {
  font-size: 16px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* 预览模式下的特殊样式 */
.recipe-step-preview-container :deep(.vue-flow__background) {
  background-color: #fffbf7;
}

.recipe-step-preview-container :deep(.vue-flow__node) {
  cursor: pointer;
  /* 移除transform transition，避免与组件内部样式冲突 */
}

/* 移除预览模式的hover transform，让组件自己处理悬停效果 */

/* 响应式设计 */
@media (max-width: 768px) {
  .preview-hint {
    padding: 10px 12px;
  }

  .hint-text {
    font-size: 13px;
    flex-direction: column;
    gap: 4px;
  }

  .hint-icon {
    font-size: 14px;
  }
}
</style>
