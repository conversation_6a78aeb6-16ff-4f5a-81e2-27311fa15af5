<template>
  <div id="recipe-step-detail">
    <a-modal
      :open="visible"
      :footer="null"
      :closable="true"
      @cancel="handleClose"
      :width="modalWidth"
      :mask-closable="false"
      class="recipe-step-detail-modal"
      :body-style="{ padding: 0, height: modalHeight }"
      centered
    >
      <!-- 弹窗标题栏 -->
      <template #title>
        <div class="modal-header">
          <div class="header-left">
            <span class="modal-title"> {{ mode === 'edit' ? '编辑' : '查看' }}菜谱步骤 </span>
            <a-tag :color="mode === 'edit' ? 'orange' : 'blue'">
              {{ mode === 'edit' ? '编辑模式' : '查看模式' }}
            </a-tag>
          </div>
          <div class="header-right">
            <a-space>
              <!-- 数据状态指示器 -->
              <div class="data-status" v-if="recipeStepStore.hasData">
                <a-tag :color="recipeStepStore.isDirty ? 'orange' : 'green'" size="small">
                  <template #icon>
                    <SaveOutlined v-if="!recipeStepStore.isDirty" />
                    <EditOutlined v-else />
                  </template>
                  {{ recipeStepStore.isDirty ? '未保存' : '已保存' }}
                </a-tag>
              </div>

              <a-button
                v-if="mode === 'edit'"
                type="primary"
                :loading="saving || recipeStepStore.isSaving"
                @click="handleSave"
                size="small"
              >
                保存到服务器
              </a-button>
              <a-button @click="toggleLayout" size="small">
                <template #icon>
                  <SwapOutlined />
                </template>
                {{ isVerticalLayout ? '水平布局' : '垂直布局' }}
              </a-button>
            </a-space>
          </div>
        </div>
      </template>

      <!-- 主体内容 -->
      <div class="modal-content" :class="{ 'vertical-layout': isVerticalLayout }">
        <!-- 左侧/上方信息面板 -->
        <div class="info-panel">
          <!-- 菜谱统计信息 -->
          <div class="stats-section">
            <h3 class="section-title">
              <DashboardOutlined />
              菜谱统计
            </h3>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-number">{{ totalSteps }}</div>
                <div class="stat-label">总步骤</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ totalDuration }}</div>
                <div class="stat-label">预计耗时(分钟)</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ parallelStepsCount }}</div>
                <div class="stat-label">可并行步骤</div>
              </div>
            </div>
          </div>

          <!-- 当前选中步骤详情 -->
          <div class="selected-step-section">
            <h3 class="section-title">
              <InfoCircleOutlined />
              {{ selectedNode ? '步骤详情' : '选择步骤' }}
            </h3>
            <div v-if="selectedNode" class="step-details">
              <div class="step-header">
                <h4 class="step-title">{{ selectedNode.data.formattedTitle }}</h4>
                <div class="step-badges">
                  <a-tag v-if="selectedNode.data.estMinutes" color="orange">
                    {{ selectedNode.data.durationText }}
                  </a-tag>
                  <a-tag v-if="selectedNode.data.parallelizable" color="green"> 可并行 </a-tag>
                </div>
              </div>

              <!-- 查看模式 -->
              <div v-if="mode === 'view'" class="step-content">
                <div class="step-field">
                  <label>步骤备注:</label>
                  <p>{{ selectedNode.data.displayNote }}</p>
                </div>
                <div class="step-field">
                  <label>执行顺序:</label>
                  <span>第 {{ selectedNode.data.orderIndex + 1 }} 步</span>
                </div>
                <div class="step-field" v-if="selectedNode.data.estMinutes">
                  <label>预计时间:</label>
                  <span>{{ selectedNode.data.estMinutes }} 分钟</span>
                </div>
              </div>

              <!-- 编辑模式 -->
              <div v-else class="step-content step-edit-form">
                <div class="step-field">
                  <label>步骤备注:</label>
                  <a-input
                    v-model:value="editingStep.note"
                    placeholder="请输入步骤备注"
                    :maxlength="200"
                    show-count
                  />
                </div>
                <div class="step-field">
                  <label>执行顺序:</label>
                  <a-input-number
                    v-model:value="editingStep.orderIndex"
                    :min="0"
                    :max="recipeStepList.length - 1"
                    style="width: 100%"
                    placeholder="步骤顺序"
                  />
                  <small class="field-hint">范围: 0 - {{ recipeStepList.length - 1 }}</small>
                </div>
                <div class="step-field">
                  <label>预计时间 (分钟):</label>
                  <a-input-number
                    v-model:value="editingStep.estMinutes"
                    :min="0"
                    :max="999"
                    style="width: 100%"
                    placeholder="预计耗时"
                  />
                </div>
                <div class="step-field">
                  <label>并行设置:</label>
                  <a-switch
                    v-model:value="editingStep.parallelizable"
                    checked-children="可并行"
                    un-checked-children="顺序执行"
                  />
                </div>

                <!-- 编辑模式下的保存按钮 -->
                <div class="edit-actions">
                  <a-button type="primary" block @click="saveStepChanges" :loading="savingStep">
                    <template #icon><SaveOutlined /></template>
                    保存修改
                  </a-button>
                  <a-button block @click="cancelStepEdit" style="margin-top: 8px">
                    <template #icon><CloseOutlined /></template>
                    取消
                  </a-button>
                </div>
              </div>
            </div>
            <div v-else class="no-selection">
              <a-empty :image="simpleImage" description="点击右侧流程图中的步骤查看详情" />
            </div>
          </div>

          <!-- 操作按钮区域 -->
          <div class="actions-section" v-if="mode === 'edit'">
            <h3 class="section-title">
              <ToolOutlined />
              操作工具
            </h3>
            <div class="action-buttons">
              <a-button block type="primary" @click="showAddStepModal" style="margin-bottom: 12px">
                <template #icon><PlusOutlined /></template>
                添加步骤
              </a-button>
              <a-button block @click="autoLayoutFlow" :loading="layouting">
                <template #icon><ApartmentOutlined /></template>
                自动布局
              </a-button>
              <a-button block @click="resetFlow">
                <template #icon><ReloadOutlined /></template>
                重置视图
              </a-button>
            </div>
          </div>
        </div>

        <!-- 右侧/下方流程图 -->
        <div class="flow-panel">
          <div class="panel-header">
            <h3 class="panel-title">
              <NodeIndexOutlined />
              步骤流程图
            </h3>
            <div class="panel-tools">
              <a-tooltip title="适应视图">
                <a-button size="small" @click="fitFlowView">
                  <ExpandOutlined />
                </a-button>
              </a-tooltip>
            </div>
          </div>
          <div class="flow-container" :style="{ height: flowHeight }">
            <RecipeStepFlow
              :recipeList="currentStepList"
              :editable="editable"
              :showToolbar="false"
              mode="full"
              :height="flowHeight"
              @nodeClick="handleNodeClick"
              @nodeUpdate="handleNodeUpdate"
              @positionChange="handlePositionChange"
              @error="handleFlowError"
              ref="flowRef"
              style="width: 100%; height: 100%"
            />
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 添加步骤弹窗 -->
    <AddStepModal
      :visible="addStepModalVisible"
      :existing-steps="currentStepList"
      :loading="creatingStep"
      @close="handleAddStepClose"
      @create="handleAddStepCreate"
    />
  </div>
</template>

<script setup lang="ts">
import RecipeStepFlow from '@/components/recipe/RecipeStepFlow.vue'
import AddStepModal from '@/components/recipe/AddStepModal.vue'
import { ref, computed, onMounted, nextTick, watch, onBeforeUnmount } from 'vue'
import { message, Empty } from 'ant-design-vue'
import { useRecipeStepStore } from '@/stores/recipeStepStore'
import {
  SwapOutlined,
  DashboardOutlined,
  InfoCircleOutlined,
  ToolOutlined,
  ApartmentOutlined,
  ReloadOutlined,
  NodeIndexOutlined,
  ExpandOutlined,
  SaveOutlined,
  CloseOutlined,
  PlusOutlined,
  EditOutlined,
} from '@ant-design/icons-vue'
import '@vue-flow/core/dist/style.css'
/* this contains the default theme, these are optional styles */
import '@vue-flow/core/dist/theme-default.css'

interface Props {
  visible: boolean
  mode: 'edit' | 'view' // 编辑模式或查看模式
  recipeStepList: any[] // 修改为 any[] 避免类型问题
  title?: string
  editable?: boolean // 是否允许编辑（即使在edit模式下也可以设为false来只读）
}

// Props定义
const props = withDefaults(defineProps<Props>(), {
  visible: false,
  mode: 'view',
  recipeStepList: () => [],
  title: '',
  editable: true,
})

// Emits定义
const emit = defineEmits<{
  close: []
  save: [recipeStepList: any[]]
}>()

// 使用 Pinia store
const recipeStepStore = useRecipeStepStore()

// 响应式数据
const saving = ref(false)
const layouting = ref(false)
const selectedNode = ref<any>(null)
const isVerticalLayout = ref(false) // 控制布局方向
const flowRef = ref<any>(null)
const savingStep = ref(false) // 单个步骤保存状态
const editingStep = ref<any>({}) // 正在编辑的步骤数据

// 添加步骤相关
const addStepModalVisible = ref(false) // 添加步骤弹窗显示状态
const creatingStep = ref(false) // 创建步骤的加载状态

// Empty 组件的简单图片
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE

// 计算属性
const modalWidth = computed(() => {
  return isVerticalLayout.value ? '90%' : '85%'
})

const modalHeight = computed(() => {
  return isVerticalLayout.value ? '85vh' : '75vh'
})

const flowHeight = computed(() => {
  return isVerticalLayout.value ? '400px' : '100%'
})

// 使用 store 中的计算属性
const totalDuration = computed(() => recipeStepStore.totalDuration)
const parallelStepsCount = computed(() => recipeStepStore.parallelStepsCount)
const totalSteps = computed(() => recipeStepStore.totalSteps)

// 当前步骤列表（统一从 store 中获取数据）
const currentStepList = computed(() => {
  // 统一使用 Store 数据，确保数据一致性
  if (recipeStepStore.hasData) {
    console.log('🔍 从Store获取步骤列表:', recipeStepStore.recipeSteps)
    return [...recipeStepStore.recipeSteps]
  }

  // 兜底：如果 Store 没有数据，使用 props（通常不会到这里）
  console.log('⚠️ Store无数据，使用props数据:', props.recipeStepList)
  return props.recipeStepList
})

// 事件处理函数
const handleClose = () => {
  selectedNode.value = null // 重置选中状态
  emit('close')
}

const handleSave = async () => {
  try {
    saving.value = true

    // 强制保存到本地存储
    await recipeStepStore.forceSave()

    // 获取最新的步骤数据，转换为可变数组
    const currentSteps = recipeStepStore.hasData
      ? [...recipeStepStore.recipeSteps]
      : props.recipeStepList

    // 发送到后端
    emit('save', currentSteps)

    message.success('保存成功')
    console.log('💾 数据已保存到服务器')
  } catch (error) {
    console.error('Save failed:', error)
    message.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 切换布局方向
const toggleLayout = () => {
  isVerticalLayout.value = !isVerticalLayout.value
  nextTick(() => {
    // 布局切换后重新适应视图
    if (flowRef.value) {
      setTimeout(() => {
        fitFlowView()
      }, 300)
    }
  })
}

// 自动布局流程图
const autoLayoutFlow = async () => {
  if (!flowRef.value) return

  try {
    layouting.value = true
    // 调用流程图组件的自动布局方法
    await flowRef.value.autoLayout()
    message.success('自动布局完成')
  } catch (error) {
    console.error('Auto layout failed:', error)
    message.error('自动布局失败')
  } finally {
    layouting.value = false
  }
}

// 重置流程图视图
const resetFlow = () => {
  if (flowRef.value) {
    flowRef.value.resetZoom()
    message.success('视图已重置')
  }
}

// 适应流程图视图
const fitFlowView = () => {
  if (flowRef.value) {
    flowRef.value.fitView()
  }
}

// 节点点击事件
const handleNodeClick = (node: any) => {
  selectedNode.value = node
  console.log('Node selected:', node)

  // 如果是编辑模式，初始化编辑数据
  if (props.mode === 'edit' && node && node.data) {
    initEditingStep(node.data)
  }
}

// 初始化编辑步骤数据
const initEditingStep = (nodeData: any) => {
  console.log('🔍 初始化编辑数据，nodeData:', nodeData)
  editingStep.value = {
    // 使用节点显示的当前值作为编辑的初始值
    title: nodeData.title || nodeData.originalStep?.title || '',
    note: nodeData.note || '',
    orderIndex: nodeData.orderIndex !== undefined ? nodeData.orderIndex : 0,
    estMinutes: nodeData.estMinutes || null,
    parallelizable: Boolean(nodeData.parallelizable),
    // 保留原始数据的其他字段（位置、ID等）
    positionX: nodeData.originalStep?.positionX || 0,
    positionY: nodeData.originalStep?.positionY || 0,
    recipeId: nodeData.originalStep?.recipeId || null,
    createTime: nodeData.originalStep?.createTime,
    updateTime: nodeData.originalStep?.updateTime,
  }
  console.log('🔍 初始化编辑数据完成:', editingStep.value)
}

// 节点更新事件 - 直接使用 originalStep，因为它在 saveStepChanges 中已经是最新数据
const handleNodeUpdate = (node: any) => {
  console.log('🔄 节点更新事件:', node)

  if (node && node.data && node.data.originalStep) {
    // 在 saveStepChanges 中，originalStep 已经被更新为最新数据
    // 所以这里直接传递 originalStep 即可
    console.log('🔍 更新 store 中的步骤数据:', node.data.originalStep)
    recipeStepStore.updateStep(node.data.originalStep)
    console.log('✅ Store 更新完成')
  } else {
    console.warn('⚠️ 节点数据无效，无法更新 store:', node)
  }
}

// 位置变化事件
const handlePositionChange = (nodes: any[]) => {
  console.log('Position changed:', nodes)

  if (nodes && nodes.length > 0) {
    // 提取步骤数据并更新 store
    const updatedSteps = nodes
      .filter((node) => node.data && node.data.originalStep)
      .map((node) => ({
        ...node.data.originalStep,
        positionX: node.position.x,
        positionY: node.position.y,
      }))

    if (updatedSteps.length > 0) {
      recipeStepStore.updateStepPositions(updatedSteps)
    }
  }
}

// 流程图错误事件
const handleFlowError = (error: string) => {
  console.error('Flow error:', error)
  message.error(error)
}

// 保存步骤修改
const saveStepChanges = async () => {
  if (!selectedNode.value || !editingStep.value) {
    message.error('没有选中的步骤或编辑数据')
    return
  }

  try {
    savingStep.value = true

    // 验证数据
    if (
      editingStep.value.orderIndex < 0 ||
      editingStep.value.orderIndex >= props.recipeStepList.length
    ) {
      message.error(`执行顺序必须在 0 到 ${props.recipeStepList.length - 1} 之间`)
      return
    }

    if (editingStep.value.estMinutes !== null && editingStep.value.estMinutes < 0) {
      message.error('预计时间不能为负数')
      return
    }

    // 更新节点数据
    const updatedNodeData = {
      ...selectedNode.value.data,
      note: editingStep.value.note,
      orderIndex: editingStep.value.orderIndex,
      estMinutes: editingStep.value.estMinutes,
      parallelizable: editingStep.value.parallelizable,
      // 更新显示相关的字段
      displayNote: editingStep.value.note || '暂无备注',
      durationText: editingStep.value.estMinutes
        ? `${editingStep.value.estMinutes}分钟`
        : '时间待定',
      hasNote: Boolean(editingStep.value.note),
      duration: editingStep.value.estMinutes || 0,
      isParallel: editingStep.value.parallelizable,
      // 重新格式化标题
      formattedTitle: `Step${editingStep.value.orderIndex + 1}.${selectedNode.value.data.title}`,
      // 更新原始步骤数据 - 包含所有编辑的字段
      originalStep: {
        ...selectedNode.value.data.originalStep,
        title: editingStep.value.title || selectedNode.value.data.title,
        note: editingStep.value.note,
        orderIndex: editingStep.value.orderIndex,
        estMinutes: editingStep.value.estMinutes,
        parallelizable: editingStep.value.parallelizable,
        // 保持位置和其他元数据不变
        positionX: editingStep.value.positionX || selectedNode.value.data.originalStep.positionX,
        positionY: editingStep.value.positionY || selectedNode.value.data.originalStep.positionY,
        recipeId: editingStep.value.recipeId || selectedNode.value.data.originalStep.recipeId,
        createTime: selectedNode.value.data.originalStep.createTime,
        updateTime: new Date().toISOString(),
      },
    }

    // 更新选中节点的数据
    selectedNode.value = {
      ...selectedNode.value,
      data: updatedNodeData,
    }

    // 调试信息
    console.log('🔍 保存步骤变更 - 选中节点:', selectedNode.value)
    console.log('🔍 保存步骤变更 - 编辑数据:', editingStep.value)
    console.log('🔍 保存步骤变更 - 更新后的节点数据:', updatedNodeData)

    // 通知父组件节点已更新，这会触发 store 更新，进而触发 watch 自动更新流程图
    handleNodeUpdate(selectedNode.value)

    message.success('步骤信息已保存')
  } catch (error) {
    console.error('保存步骤失败:', error)
    message.error('保存失败，请重试')
  } finally {
    savingStep.value = false
  }
}

// 取消编辑
const cancelStepEdit = () => {
  if (selectedNode.value && selectedNode.value.data) {
    // 重置编辑数据为原始值
    initEditingStep(selectedNode.value.data)
    message.info('已取消编辑')
  }
}

// ====== 添加步骤相关方法 ======

// 显示添加步骤弹窗
const showAddStepModal = () => {
  addStepModalVisible.value = true
}

// 处理添加步骤弹窗关闭
const handleAddStepClose = () => {
  addStepModalVisible.value = false
}

// 处理创建新步骤
const handleAddStepCreate = async (stepData: any) => {
  try {
    creatingStep.value = true

    // 使用 store 添加新步骤
    recipeStepStore.addStep(stepData)

    // 关闭弹窗
    addStepModalVisible.value = false

    message.success('新步骤创建成功')

    // 刷新流程图显示，添加新步骤后需要重新布局
    if (flowRef.value) {
      setTimeout(() => {
        flowRef.value.initializeData(false) // 不自动适应视图
      }, 100)
    }
  } catch (error) {
    console.error('创建步骤失败:', error)
    message.error('创建步骤失败，请重试')
  } finally {
    creatingStep.value = false
  }
}

// 初始化 store 数据（现在数据应该已经在 Store 中了）
const initializeStoreData = () => {
  // 由于现在 RecipeDetailPage 已经将数据存储到 Store 中
  // 这里主要是确保数据存在
  if (!recipeStepStore.hasData && props.recipeStepList.length > 0) {
    const recipeId = props.recipeStepList[0]?.recipeId
    if (recipeId) {
      console.log(`🔄 初始化菜谱 ${recipeId} 的数据到 store`)
      recipeStepStore.loadRecipeSteps(recipeId, props.recipeStepList)
    }
  } else {
    console.log('✅ Store 中已有数据，无需重新初始化')
  }
}

// 监听 props 变化，更新 store 数据
watch(
  () => props.recipeStepList,
  (newSteps) => {
    if (newSteps && newSteps.length > 0) {
      initializeStoreData()
    }
  },
  { immediate: true, deep: true },
)

// 监听 store 数据变化，通知流程图更新（但不重置视图）
watch(
  () => recipeStepStore.recipeSteps,
  (newSteps, oldSteps) => {
    if (flowRef.value && recipeStepStore.hasData && props.visible) {
      console.log('🔍 Store 数据变化，更新流程图')
      console.log('🔍 新数据:', newSteps)
      console.log('🔍 旧数据:', oldSteps)

      nextTick(() => {
        setTimeout(() => {
          flowRef.value.updateNodeData()
        }, 50)
      })
    }
  },
  { deep: true },
)

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      // 弹窗打开时初始化数据
      initializeStoreData()
    }
  },
)

// 组件挂载时的处理
onMounted(() => {
  // 根据屏幕尺寸决定默认布局
  if (window.innerWidth < 1200) {
    isVerticalLayout.value = true
  }

  // 如果弹窗已经显示，初始化数据
  if (props.visible) {
    initializeStoreData()
  }
})

// 组件卸载前保存数据（如果有未保存的修改）
onBeforeUnmount(async () => {
  if (recipeStepStore.isDirty) {
    console.log('🔄 RecipeStepDetail组件卸载前保存数据')
    await recipeStepStore.forceSave()
  }
  // 注意：不在这里清空数据，因为这是子组件，由父组件(RecipeDetailPage)负责清空
})
</script>

<style scoped>
@import '@vue-flow/core/dist/style.css';

/* import the default theme, this is optional but generally recommended */
@import '@vue-flow/core/dist/theme-default.css';

/* 弹窗整体样式 */
.recipe-step-detail-modal :deep(.ant-modal) {
  max-width: 95vw;
  max-height: 95vh;
}

.recipe-step-detail-modal :deep(.ant-modal-content) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.recipe-step-detail-modal :deep(.ant-modal-body) {
  flex: 1;
  overflow: hidden;
}

/* 弹窗标题栏 */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  /* 添加右侧边距，为模态框关闭按钮留出空间 */
  padding-right: 40px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  /* 确保左侧内容不会过度扩展 */
  flex: 1;
  min-width: 0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  /* 防止标题过长时影响布局 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.header-right {
  flex-shrink: 0;
  /* 确保按钮组不会与关闭按钮重叠 */
  margin-left: 16px;
}

/* 主体内容布局 */
.modal-content {
  display: flex;
  height: 100%;
  gap: 16px;
  padding: 16px;
  background: #fafafa;
}

/* 垂直布局 */
.modal-content.vertical-layout {
  flex-direction: column;
}

.modal-content.vertical-layout .info-panel {
  width: 100%;
  height: auto;
  max-height: 40%;
}

.modal-content.vertical-layout .flow-panel {
  width: 100%;
  flex: 1;
}

/* 垂直布局模式下的标题栏调整 */
.vertical-layout ~ .modal-header,
.recipe-step-detail-modal.vertical-layout .modal-header {
  /* 在垂直布局模式下增加额外的右侧边距 */
  padding-right: 45px;
}

/* 确保在垂直布局模式下按钮不会换行 */
.vertical-layout ~ .modal-header .header-right,
.recipe-step-detail-modal.vertical-layout .header-right {
  white-space: nowrap;
}

/* 信息面板样式 */
.info-panel {
  width: 320px;
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
}

/* 流程图面板样式 */
.flow-panel {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.panel-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-tools {
  display: flex;
  gap: 8px;
}

.flow-container {
  flex: 1;
  overflow: hidden;
}

/* 节标题样式 */
.section-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #595959;
  display: flex;
  align-items: center;
  gap: 6px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

/* 统计信息样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 12px;
}

.stat-item {
  text-align: center;
  padding: 12px 8px;
  background: #fff7ed;
  border-radius: 6px;
  border: 1px solid #fed7aa;
}

.stat-number {
  font-size: 20px;
  font-weight: 700;
  color: #ff7a00;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #8c8c8c;
  font-weight: 500;
}

/* 选中步骤详情样式 */
.selected-step-section {
  flex: 1;
}

.step-details {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
}

.step-header {
  margin-bottom: 12px;
}

.step-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.step-badges {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.step-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.step-field {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.step-field label {
  font-size: 12px;
  font-weight: 600;
  color: #8c8c8c;
}

.step-field p,
.step-field span {
  font-size: 13px;
  color: #262626;
  margin: 0;
}

.no-selection {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-buttons .ant-btn {
  height: 36px;
  font-size: 13px;
}

/* 编辑表单样式 */
.step-edit-form {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #e9ecef;
}

.step-edit-form .step-field {
  margin-bottom: 16px;
}

.step-edit-form .step-field:last-of-type {
  margin-bottom: 0;
}

.step-edit-form label {
  display: block;
  margin-bottom: 6px;
  font-size: 13px;
  font-weight: 600;
  color: #495057;
}

.step-edit-form .ant-input,
.step-edit-form .ant-input-number {
  border-radius: 6px;
  border: 1px solid #ced4da;
  transition: all 0.2s ease;
}

.step-edit-form .ant-input:focus,
.step-edit-form .ant-input-number:focus {
  border-color: #ff7a00;
  box-shadow: 0 0 0 2px rgba(255, 122, 0, 0.1);
}

.step-edit-form .ant-switch {
  background-color: #6c757d;
}

.step-edit-form .ant-switch-checked {
  background-color: #ff7a00;
}

.field-hint {
  display: block;
  margin-top: 4px;
  color: #6c757d;
  font-size: 11px;
  font-style: italic;
}

/* 编辑操作按钮 */
.edit-actions {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
}

.edit-actions .ant-btn {
  height: 36px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.edit-actions .ant-btn-primary {
  background: #ff7a00;
  border-color: #ff7a00;
  box-shadow: 0 2px 4px rgba(255, 122, 0, 0.2);
}

.edit-actions .ant-btn-primary:hover {
  background: #e66d00;
  border-color: #e66d00;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(255, 122, 0, 0.3);
}

.edit-actions .ant-btn:not(.ant-btn-primary) {
  background: #f8f9fa;
  border-color: #dee2e6;
  color: #6c757d;
}

.edit-actions .ant-btn:not(.ant-btn-primary):hover {
  background: #e9ecef;
  border-color: #adb5bd;
  color: #495057;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .modal-content {
    flex-direction: column;
  }

  .info-panel {
    width: 100%;
    height: auto;
    max-height: 40%;
  }

  .flow-panel {
    width: 100%;
    flex: 1;
  }

  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .modal-content {
    padding: 12px;
    gap: 12px;
  }

  .info-panel {
    padding: 12px;
    max-height: 45%;
  }

  .panel-header {
    padding: 8px 12px;
  }

  .panel-title {
    font-size: 14px;
  }

  .modal-title {
    font-size: 16px;
  }

  .header-left {
    gap: 8px;
  }

  /* 在小屏幕上增加更多右侧边距，防止按钮重叠 */
  .modal-header {
    padding-right: 50px;
  }

  .header-right {
    margin-left: 12px;
  }

  /* 在小屏幕上调整按钮间距 */
  .header-right .ant-space {
    gap: 8px !important;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .stat-item {
    padding: 8px 6px;
  }

  .stat-number {
    font-size: 16px;
  }

  .stat-label {
    font-size: 11px;
  }
}

/* 橙色系主题优化 */
.recipe-step-detail-modal :deep(.ant-modal-header) {
  background: linear-gradient(135deg, #fff7ed 0%, #ffedd5 100%);
  border-bottom: 1px solid #fed7aa;
}

.recipe-step-detail-modal :deep(.ant-tag-orange) {
  background: #ff7a00;
  border-color: #ff7a00;
  color: white;
}

.recipe-step-detail-modal :deep(.ant-btn-primary) {
  background: #ff7a00;
  border-color: #ff7a00;
}

.recipe-step-detail-modal :deep(.ant-btn-primary:hover) {
  background: #e66d00;
  border-color: #e66d00;
}

/* 确保模态框关闭按钮有正确的层级和位置 */
.recipe-step-detail-modal :deep(.ant-modal-close) {
  z-index: 1001;
  position: absolute;
  top: 16px;
  right: 16px;
}

.recipe-step-detail-modal :deep(.ant-modal-close-x) {
  width: 22px;
  height: 22px;
  line-height: 22px;
  font-size: 16px;
}
</style>
