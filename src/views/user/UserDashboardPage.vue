<template>
  <div class="dashboard-container">
    <a-row :gutter="[24, 24]">
      <!-- 用户信息卡片 -->
      <a-col :xs="24" :lg="8">
        <a-card class="user-card">
          <div class="user-info">
            <UserAvatar
              :src="userAvatar"
              :userName="userName || '未登录用户'"
              :userRole="userRole"
              size="extra-large"
              :showInfo="false"
              :showOnlineStatus="true"
              :isOnline="true"
              :verified="true"
              class="user-avatar"
            />
            <h3 class="user-name">{{ userName || '未登录用户' }}</h3>
            <a-button type="primary" @click="$router.push('/profile')" class="edit-profile-btn">
              编辑资料
            </a-button>
          </div>
        </a-card>
      </a-col>

      <!-- 主要内容区域 -->
      <a-col :xs="24" :lg="16">
        <div class="dashboard-content">
          <!-- 已保存的食谱 -->
          <section class="content-section">
            <h2 class="section-title">已保存的食谱</h2>
            <a-row :gutter="[16, 16]">
              <a-col v-for="recipe in savedRecipes" :key="recipe.id" :xs="24" :sm="8">
                <RecipeCard
                  :recipe="convertToRecipeFormat(recipe)"
                  size="small"
                  :isFavorited="true"
                  :showActions="true"
                  @click="handleRecipeClick"
                  @toggle-favorite="handleToggleFavorite"
                  @share="handleShare"
                />
              </a-col>
            </a-row>

            <!-- 空状态 -->
            <a-empty
              v-if="savedRecipes.length === 0"
              description="暂无保存的食谱"
              class="empty-state"
            >
              <a-button type="primary" @click="$router.push('/recipes')"> 浏览食谱 </a-button>
            </a-empty>
          </section>

          <!-- 购物清单 -->
          <section class="content-section">
            <h2 class="section-title">购物清单</h2>
            <a-card class="grocery-card">
              <a-list :data-source="groceryItems" :locale="{ emptyText: '暂无购物项目' }">
                <template #renderItem="{ item }">
                  <a-list-item>
                    <span>{{ item.name }}</span>
                  </a-list-item>
                </template>
              </a-list>

              <div class="grocery-actions">
                <a-button type="primary" @click="$router.push('/grocery')"> 查看完整清单 </a-button>
              </div>
            </a-card>
          </section>

          <!-- 膳食计划 -->
          <section class="content-section">
            <h2 class="section-title">膳食计划</h2>
            <a-row :gutter="[16, 16]">
              <a-col v-for="plan in mealPlans" :key="plan.day" :xs="24" :md="12">
                <a-card :title="plan.day" class="meal-plan-card">
                  <div class="meal-plan-content">
                    <div class="meal-item">
                      <span class="meal-type">早餐:</span>
                      <span class="meal-name">{{ plan.breakfast }}</span>
                    </div>
                    <div class="meal-item">
                      <span class="meal-type">午餐:</span>
                      <span class="meal-name">{{ plan.lunch }}</span>
                    </div>
                    <div class="meal-item">
                      <span class="meal-type">晚餐:</span>
                      <span class="meal-name">{{ plan.dinner }}</span>
                    </div>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </section>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useLoginUserStore } from '@/stores/userLoginStore'
import { UserAvatar, RecipeCard } from '@/components'

/**
 * 用户仪表板页面
 * 对应HTML原型中的user-dashboard.html
 */

interface SavedRecipe {
  id: string
  name: string
  coverUrl?: string
}

interface Recipe {
  id?: number
  name?: string
  introduction?: string
  coverKey?: string
  tags?: string[]
  category?: string
  stepCount?: number
  cookTime?: number
  servings?: number
  difficulty?: 'easy' | 'medium' | 'hard'
  rating?: number
  viewCount?: number
  favoriteCount?: number
  estimatedCost?: number
}

interface GroceryItem {
  id: string
  name: string
}

interface MealPlan {
  day: string
  breakfast: string
  lunch: string
  dinner: string
}

const router = useRouter()
const userStore = useLoginUserStore()

// 响应式数据
const savedRecipes = ref<SavedRecipe[]>([
  { id: '1', name: '奶油意大利面', coverUrl: 'https://picsum.photos/seed/pasta2/300/200' },
  { id: '2', name: '巧克力曲奇', coverUrl: 'https://picsum.photos/seed/cookies/300/200' },
  { id: '3', name: '牛油果沙拉', coverUrl: 'https://picsum.photos/seed/avocado/300/200' },
])

const groceryItems = ref<GroceryItem[]>([
  { id: '1', name: '鸡蛋、牛奶、面包' },
  { id: '2', name: '鸡胸肉、西兰花、米饭' },
  { id: '3', name: '番茄、生菜、黄瓜' },
])

const mealPlans = ref<MealPlan[]>([
  {
    day: '周一',
    breakfast: '燕麦粥',
    lunch: '烤鸡沙拉',
    dinner: '意大利肉酱面',
  },
  {
    day: '周二',
    breakfast: '果昔碗',
    lunch: '火鸡三明治',
    dinner: '蔬菜炒饭',
  },
])

const defaultCover = 'https://picsum.photos/seed/recipe/300/200'

// 计算属性
const userName = computed(() => userStore.loginUser?.userName)
const userAvatar = computed(() => userStore.loginUser?.userAvatar)
const userRole = computed(() => {
  const role = userStore.loginUser?.userRole
  if (role === 'admin') return '管理员'
  if (role === 'user') return '普通用户'
  return '游客'
})

// 转换数据格式
const convertToRecipeFormat = (savedRecipe: SavedRecipe): Recipe => {
  return {
    id: Number(savedRecipe.id),
    name: savedRecipe.name,
    coverKey: savedRecipe.coverUrl,
    introduction: '美味的家常菜谱',
    tags: ['家常', '美味'],
    cookTime: 30,
    servings: 2,
    difficulty: 'easy' as const,
    rating: 4.5,
    stepCount: 5,
    estimatedCost: 25,
  }
}

// 事件处理器
const handleRecipeClick = (recipe: Recipe) => {
  router.push(`/recipes/${recipe.id}`)
}

const handleToggleFavorite = (recipeId: number, isFavorited: boolean) => {
  // TODO: 实现收藏/取消收藏逻辑
  message.success(isFavorited ? '已取消收藏' : '已添加到收藏')
}

const handleShare = (recipe: Recipe) => {
  // TODO: 实现分享功能
  message.success(`已分享食谱：${recipe.name}`)
}

// 生命周期
onMounted(async () => {
  // TODO: 加载用户仪表板数据
  // await loadDashboardData()
})
</script>

<style lang="less" scoped>
.dashboard-container {
  .user-card {
    text-align: center;

    .user-info {
      .user-avatar {
        margin-bottom: 16px;
      }

      .user-name {
        font-size: 20px;
        font-weight: 600;
        margin: 16px 0;
        color: #1a1a1a;
      }

      .edit-profile-btn {
        background: #ff7a00;
        border-color: #ff7a00;

        &:hover {
          background: rgba(255, 122, 0, 0.9);
          border-color: rgba(255, 122, 0, 0.9);
        }
      }
    }
  }

  .dashboard-content {
    .content-section {
      margin-bottom: 32px;

      .section-title {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 16px;
        color: #1a1a1a;
      }
    }

    .grocery-card {
      .grocery-actions {
        margin-top: 16px;
        text-align: center;
      }
    }

    .meal-plan-card {
      border-radius: 12px;
      background: #fff2e8;

      :deep(.ant-card-head) {
        background: #ff7a00;
        border-color: #ff7a00;

        .ant-card-head-title {
          color: #fff;
          font-weight: 600;
        }
      }

      .meal-plan-content {
        .meal-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;

          .meal-type {
            font-weight: 500;
            color: #666;
          }

          .meal-name {
            color: #1a1a1a;
          }
        }
      }
    }

    .empty-state {
      margin: 40px 0;
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .dashboard-container {
    .user-card .user-info .user-name {
      font-size: 18px;
    }
  }
}
</style>
