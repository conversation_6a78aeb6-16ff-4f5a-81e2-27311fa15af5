import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActive<PERSON><PERSON>, createPinia } from 'pinia'
import { useRecipeStepStore } from '../recipeStepStore'

// Mock the API
vi.mock('@/api/recipeController', () => ({
  saveRecipeStepsUsingPost: vi.fn(),
  getRecipeUsingGet: vi.fn(),
}))

describe('RecipeStepStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    // Clear localStorage before each test
    localStorage.clear()
  })

  it('should initialize with correct default values', () => {
    const store = useRecipeStepStore()
    
    expect(store.currentRecipeId).toBeNull()
    expect(store.recipeSteps).toEqual([])
    expect(store.isDirty).toBe(false)
    expect(store.isSaving).toBe(false)
    expect(store.isLoadingFromServer).toBe(false)
    expect(store.hasData).toBe(false)
  })

  it('should handle string IDs correctly', () => {
    const store = useRecipeStepStore()
    const testRecipeId = '12345'
    const testSteps = [
      {
        id: '1',
        title: 'Test Step',
        note: 'Test Note',
        orderIndex: 1,
        estMinutes: 10,
        parallelizable: false,
        positionX: 100,
        positionY: 200,
        recipeId: testRecipeId,
      }
    ]

    store.loadRecipeSteps(testRecipeId, testSteps)
    
    expect(store.currentRecipeId).toBe(testRecipeId)
    expect(store.recipeSteps).toEqual(testSteps)
    expect(store.hasData).toBe(true)
  })

  it('should save to localStorage with string IDs', () => {
    const store = useRecipeStepStore()
    const testRecipeId = '12345'
    const testSteps = [
      {
        id: '1',
        title: 'Test Step',
        note: 'Test Note',
        orderIndex: 1,
        estMinutes: 10,
        parallelizable: false,
        positionX: 100,
        positionY: 200,
        recipeId: testRecipeId,
      }
    ]

    store.loadRecipeSteps(testRecipeId, testSteps)
    
    // Check if data is saved to localStorage
    const savedData = localStorage.getItem(`chefs_way_recipe_steps_${testRecipeId}`)
    expect(savedData).toBeTruthy()
    
    const parsedData = JSON.parse(savedData!)
    expect(parsedData).toEqual(testSteps)
  })

  it('should handle saveToServer method', async () => {
    const store = useRecipeStepStore()
    
    // Test when no data
    const result = await store.saveToServer()
    expect(result.success).toBe(false)
    expect(result.error).toBe('无数据需要保存')
  })
})
