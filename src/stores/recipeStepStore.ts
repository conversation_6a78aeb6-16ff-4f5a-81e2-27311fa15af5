import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'

// 本地存储的键名前缀
const STORAGE_PREFIX = 'chefs_way_recipe_steps_'
const STORAGE_META_KEY = 'chefs_way_recipe_meta'

// 菜谱步骤接口定义
interface RecipeStep {
  id?: string | null // recipeStepId，在新建步骤时可能为空
  title: string
  note: string
  orderIndex: number
  estMinutes: number | null
  parallelizable: number | boolean
  positionX: number
  positionY: number
  recipeId: string | null
  createTime?: string
  updateTime?: string
}

// 本地存储元数据
interface StorageMeta {
  lastCleanup: string
  recipeIds: string[]
}

// 防抖函数
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
): (...args: Parameters<T>) => void {
  let timeout: number | null = null
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export const useRecipeStepStore = defineStore('recipeStep', () => {
  // ==================== 状态定义 ====================

  // 当前菜谱ID
  const currentRecipeId = ref<string | null>(null)

  // 当前菜谱的步骤列表
  const recipeSteps = ref<RecipeStep[]>([])

  // 是否有未保存的修改
  const isDirty = ref(false)

  // 最后保存时间
  const lastSaved = ref<string | null>(null)

  // 是否正在保存
  const isSaving = ref(false)

  // 是否正在从服务器加载数据
  const isLoadingFromServer = ref(false)

  // 加载错误信息
  const loadError = ref<string | null>(null)

  // ==================== 计算属性 ====================

  // 步骤总数
  const totalSteps = computed(() => recipeSteps.value.length)

  // 总预计时间
  const totalDuration = computed(() => {
    return recipeSteps.value.reduce((total, step) => {
      return total + (step.estMinutes || 0)
    }, 0)
  })

  // 可并行步骤数量
  const parallelStepsCount = computed(() => {
    return recipeSteps.value.filter((step) => step.parallelizable).length
  })

  // 是否有数据
  const hasData = computed(() => {
    return currentRecipeId.value !== null && recipeSteps.value.length > 0
  })

  // ==================== 本地存储操作 ====================

  // 获取菜谱步骤的存储键名
  const getStorageKey = (recipeId: string): string => {
    return `${STORAGE_PREFIX}${recipeId}`
  }

  // 从localStorage加载步骤数据
  const loadFromStorage = (recipeId: string): RecipeStep[] => {
    try {
      const key = getStorageKey(recipeId)
      const data = localStorage.getItem(key)
      if (data) {
        const parsed = JSON.parse(data)
        console.log(`📖 从本地存储加载菜谱 ${recipeId} 的步骤:`, parsed)
        return Array.isArray(parsed) ? parsed : []
      }
    } catch (error) {
      console.error('❌ 从localStorage加载数据失败:', error)
    }
    return []
  }

  // 保存步骤数据到localStorage
  const saveToStorage = (recipeId: string, steps: RecipeStep[]): boolean => {
    try {
      const key = getStorageKey(recipeId)
      const data = JSON.stringify(steps)
      localStorage.setItem(key, data)

      // 更新元数据
      updateStorageMeta(recipeId)

      lastSaved.value = new Date().toISOString()
      console.log(`💾 已保存菜谱 ${recipeId} 的步骤到本地存储`)
      return true
    } catch (error) {
      console.error('❌ 保存到localStorage失败:', error)
      return false
    }
  }

  // 更新存储元数据
  const updateStorageMeta = (recipeId: string) => {
    try {
      let meta: StorageMeta
      const existingMeta = localStorage.getItem(STORAGE_META_KEY)

      if (existingMeta) {
        meta = JSON.parse(existingMeta)
      } else {
        meta = {
          lastCleanup: new Date().toISOString(),
          recipeIds: [],
        }
      }

      // 添加当前菜谱ID到列表中（去重）
      if (!meta.recipeIds.includes(recipeId)) {
        meta.recipeIds.push(recipeId)
      }

      localStorage.setItem(STORAGE_META_KEY, JSON.stringify(meta))
    } catch (error) {
      console.error('❌ 更新存储元数据失败:', error)
    }
  }

  // 防抖保存函数
  const debouncedSave = debounce((recipeId: string, steps: RecipeStep[]) => {
    if (recipeId && steps) {
      saveToStorage(recipeId, steps)
      isDirty.value = false
    }
  }, 300)

  // ==================== API调用方法 ====================

  // 从服务器加载菜谱数据
  const loadRecipeFromServer = async (recipeId: string) => {
    try {
      isLoadingFromServer.value = true
      loadError.value = null

      console.log(`🌐 从服务器加载菜谱 ${recipeId} 的数据...`)

      // 动态导入API方法，避免循环依赖
      const { getRecipeUsingGet } = await import('@/api/recipeController')

      const response = await getRecipeUsingGet({ id: recipeId })

      if (response.data.code === 0 && response.data.data) {
        const recipeData = response.data.data
        console.log(`✅ 服务器返回菜谱数据:`, recipeData)

        // 提取步骤数据并转换为统一格式
        const serverStepsRaw = recipeData.steps || []
        const serverSteps: RecipeStep[] = serverStepsRaw.map((step: any) => ({
          id: step.id, // 这是 recipeStepId
          title: step.title || '',
          note: step.note || '',
          orderIndex: step.orderIndex || 0,
          estMinutes: step.estMinutes,
          parallelizable: step.parallelizable || 0,
          positionX: step.positionX ? Number(step.positionX) : 0,
          positionY: step.positionY ? Number(step.positionY) : 0,
          recipeId: step.recipeId || recipeId,
          createTime: step.createTime,
          updateTime: step.updateTime,
        }))

        // 使用强制刷新模式加载数据
        loadRecipeSteps(recipeId, serverSteps, true)

        console.log(`✅ 从服务器成功加载菜谱 ${recipeId} (${serverSteps.length} 步)`)
        return { success: true, data: recipeData }
      } else {
        const errorMsg = response.data.message || '获取菜谱数据失败'
        loadError.value = errorMsg
        console.error(`❌ 服务器返回错误: ${errorMsg}`)
        return { success: false, error: errorMsg }
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '网络请求失败'
      loadError.value = errorMsg
      console.error(`❌ 从服务器加载菜谱 ${recipeId} 失败:`, error)

      // 网络错误时，尝试使用本地缓存数据
      const localSteps = loadFromStorage(recipeId)
      if (localSteps.length > 0) {
        console.log(`🔄 网络错误，使用本地缓存数据 (${localSteps.length} 步)`)
        loadRecipeSteps(recipeId, localSteps, false)
        return { success: false, error: errorMsg, fallbackToLocal: true }
      }

      return { success: false, error: errorMsg }
    } finally {
      isLoadingFromServer.value = false
    }
  }

  // ==================== 主要操作方法 ====================

  // 加载菜谱步骤 - 实现单一菜谱管理
  const loadRecipeSteps = (
    recipeId: string,
    serverSteps?: RecipeStep[],
    forceRefresh: boolean = false,
  ) => {
    console.log(`🔄 加载菜谱 ${recipeId} 的步骤... (强制刷新: ${forceRefresh})`)

    // 如果当前已经有其他菜谱的数据，先清空
    if (currentRecipeId.value !== null && currentRecipeId.value !== recipeId) {
      console.log(`🧹 清空之前菜谱 ${currentRecipeId.value} 的数据，加载新菜谱 ${recipeId}`)
      clearCurrentData()
    }

    currentRecipeId.value = recipeId

    if (forceRefresh) {
      // 强制刷新模式：优先使用服务器数据
      if (serverSteps && serverSteps.length > 0) {
        recipeSteps.value = [...serverSteps]
        isDirty.value = false
        // 立即保存到本地存储
        saveToStorage(recipeId, recipeSteps.value)
        console.log(`✅ 强制刷新：使用服务器步骤数据并保存到本地 (${serverSteps.length} 步)`)
      } else {
        // 服务器没有数据，尝试使用本地数据作为备选
        const localSteps = loadFromStorage(recipeId)
        if (localSteps.length > 0) {
          recipeSteps.value = localSteps
          isDirty.value = false
          console.log(`⚠️ 服务器无数据，使用本地备选数据 (${localSteps.length} 步)`)
        } else {
          recipeSteps.value = []
          isDirty.value = false
          console.log(`ℹ️ 无步骤数据`)
        }
      }
    } else {
      // 常规模式：优先使用本地存储的数据
      const localSteps = loadFromStorage(recipeId)

      if (localSteps.length > 0) {
        // 使用本地数据
        recipeSteps.value = localSteps
        isDirty.value = false
        console.log(`✅ 使用本地存储的步骤数据 (${localSteps.length} 步)`)
      } else if (serverSteps && serverSteps.length > 0) {
        // 使用服务器数据
        recipeSteps.value = [...serverSteps]
        isDirty.value = false
        // 立即保存到本地存储
        saveToStorage(recipeId, recipeSteps.value)
        console.log(`✅ 使用服务器步骤数据并保存到本地 (${serverSteps.length} 步)`)
      } else {
        // 无数据
        recipeSteps.value = []
        isDirty.value = false
        console.log(`ℹ️ 无步骤数据`)
      }
    }
  }

  // 更新单个步骤
  const updateStep = (updatedStep: RecipeStep) => {
    // 优先使用 id 查找，如果没有则使用 orderIndex
    const index = updatedStep.id
      ? recipeSteps.value.findIndex((step) => step.id === updatedStep.id)
      : recipeSteps.value.findIndex((step) => step.orderIndex === updatedStep.orderIndex)

    if (index !== -1) {
      recipeSteps.value[index] = { ...updatedStep }
      isDirty.value = true

      // 自动保存
      if (currentRecipeId.value) {
        debouncedSave(currentRecipeId.value, recipeSteps.value)
      }

      console.log(`📝 更新步骤 ${updatedStep.orderIndex}`)
    }
  }

  // 添加新步骤
  const addStep = (newStep: RecipeStep) => {
    // 更新后续步骤的orderIndex
    recipeSteps.value.forEach((step) => {
      if (step.orderIndex >= newStep.orderIndex) {
        step.orderIndex += 1
      }
    })

    // 插入新步骤
    const insertIndex = recipeSteps.value.findIndex((step) => step.orderIndex > newStep.orderIndex)

    if (insertIndex === -1) {
      recipeSteps.value.push(newStep)
    } else {
      recipeSteps.value.splice(insertIndex, 0, newStep)
    }

    isDirty.value = true

    // 自动保存
    if (currentRecipeId.value) {
      debouncedSave(currentRecipeId.value, recipeSteps.value)
    }

    console.log(`➕ 添加新步骤:`, newStep)
  }

  // 删除步骤
  const removeStep = (orderIndex: number) => {
    const stepIndex = recipeSteps.value.findIndex((step) => step.orderIndex === orderIndex)

    if (stepIndex !== -1) {
      // 删除步骤
      recipeSteps.value.splice(stepIndex, 1)

      // 更新后续步骤的orderIndex
      recipeSteps.value.forEach((step) => {
        if (step.orderIndex > orderIndex) {
          step.orderIndex -= 1
        }
      })

      isDirty.value = true

      // 自动保存
      if (currentRecipeId.value) {
        debouncedSave(currentRecipeId.value, recipeSteps.value)
      }

      console.log(`🗑️ 删除步骤 ${orderIndex}`)
    }
  }

  // 批量更新步骤位置
  const updateStepPositions = (updatedSteps: RecipeStep[]) => {
    recipeSteps.value = [...updatedSteps]
    isDirty.value = true

    // 自动保存
    if (currentRecipeId.value) {
      debouncedSave(currentRecipeId.value, recipeSteps.value)
    }

    console.log(`📍 批量更新步骤位置`)
  }

  // 强制立即保存
  const forceSave = async (): Promise<boolean> => {
    if (!currentRecipeId.value || !hasData.value) {
      console.warn('⚠️ 无数据需要保存')
      return false
    }

    isSaving.value = true

    try {
      const success = saveToStorage(currentRecipeId.value, recipeSteps.value)
      if (success) {
        isDirty.value = false
        console.log('✅ 强制保存成功')
      }
      return success
    } finally {
      isSaving.value = false
    }
  }

  // 清空当前数据 - 用于页面切换时的数据清理
  const clearCurrentData = () => {
    const previousRecipeId = currentRecipeId.value
    const previousStepCount = recipeSteps.value.length

    currentRecipeId.value = null
    recipeSteps.value = []
    isDirty.value = false
    lastSaved.value = null

    console.log(`🧹 清空当前数据 - 之前菜谱ID: ${previousRecipeId}, 步骤数: ${previousStepCount}`)
  }

  // ==================== 工具方法 ====================

  // 根据 recipeStepId 查找步骤
  const findStepById = (stepId: number | string): RecipeStep | null => {
    return recipeSteps.value.find((step) => step.id === stepId) || null
  }

  // 根据 orderIndex 查找步骤
  const findStepByOrderIndex = (orderIndex: number): RecipeStep | null => {
    return recipeSteps.value.find((step) => step.orderIndex === orderIndex) || null
  }

  // 获取当前状态信息 - 用于调试
  const getCurrentInfo = () => {
    return {
      currentRecipeId: currentRecipeId.value,
      stepCount: recipeSteps.value.length,
      isDirty: isDirty.value,
      hasData: hasData.value,
      lastSaved: lastSaved.value,
    }
  }

  // 获取存储使用情况
  const getStorageInfo = () => {
    try {
      let totalSize = 0
      let recipeCount = 0

      // 计算所有相关键的大小
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && key.startsWith(STORAGE_PREFIX)) {
          const value = localStorage.getItem(key)
          if (value) {
            totalSize += value.length
            recipeCount += 1
          }
        }
      }

      return {
        totalSize: totalSize, // 字符数
        approximateMB: (totalSize / 1024 / 1024).toFixed(2),
        recipeCount: recipeCount,
      }
    } catch (error) {
      console.error('❌ 获取存储信息失败:', error)
      return null
    }
  }

  // 清理旧数据
  const cleanupOldData = (keepDays: number = 7) => {
    try {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - keepDays)

      const meta = localStorage.getItem(STORAGE_META_KEY)
      if (meta) {
        const parsedMeta: StorageMeta = JSON.parse(meta)

        // 这里可以根据实际需求实现清理逻辑
        // 比如清理超过指定天数的数据
        console.log(`🧹 执行数据清理 (保留${keepDays}天)`)
      }
    } catch (error) {
      console.error('❌ 清理数据失败:', error)
    }
  }

  // ==================== 返回 Store API ====================

  return {
    // 状态
    currentRecipeId: readonly(currentRecipeId),
    recipeSteps: readonly(recipeSteps),
    isDirty: readonly(isDirty),
    lastSaved: readonly(lastSaved),
    isSaving: readonly(isSaving),
    isLoadingFromServer: readonly(isLoadingFromServer),
    loadError: readonly(loadError),

    // 计算属性
    totalSteps,
    totalDuration,
    parallelStepsCount,
    hasData,

    // API方法
    loadRecipeFromServer,

    // 主要方法
    loadRecipeSteps,
    updateStep,
    addStep,
    removeStep,
    updateStepPositions,
    forceSave,
    clearCurrentData,

    // 工具方法
    findStepById,
    findStepByOrderIndex,
    getCurrentInfo,
    getStorageInfo,
    cleanupOldData,
  }
})

// 导出类型
export type { RecipeStep }
