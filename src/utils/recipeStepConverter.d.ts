// 类型声明文件 for recipeStepConverter.js

export interface ConvertOptions {
  nodeType?: string
  defaultSpacing?: { x: number; y: number }
  defaultStart?: { x: number; y: number }
}

export interface LayoutOptions {
  type?: 'grid' | 'flow' | 'cascade'
  spacing?: { x: number; y: number }
  start?: { x: number; y: number }
  columns?: number
}

export declare function convertRecipeStepsToNodes(
  steps: any[],
  options?: ConvertOptions
): any[]

export declare function generateStepEdges(nodes: any[]): any[]

export declare function autoLayoutNodes(
  nodes: any[],
  layoutOptions?: LayoutOptions
): any[]

export declare function convertNodesToRecipeSteps(nodes: any[]): any[]

export declare function findNodeByOrderIndex(
  nodes: any[],
  orderIndex: number
): any | null

export declare function batchUpdatePositionsByOrderIndex(
  nodes: any[],
  updateAPI: (orderIndex: number, update: any) => Promise<any>
): Promise<{ success: number; failed: number; total: number }>

export declare function debugStepsData(steps: any[]): void