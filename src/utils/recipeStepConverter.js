// utils/recipeStepConverter.js

/**
 * 将后端 RecipeStep 数组转换为 VueFlow 节点格式
 * @param {Array} steps - RecipeStep 数组
 * @param {Object} options - 转换选项
 * @returns {Array} VueFlow 节点数组
 */
export const convertRecipeStepsToNodes = (steps, options = {}) => {
  const {
    nodeType = 'recipeStep',
    defaultSpacing = { x: 250, y: 180 }, // 增加间距适应更大的节点
    defaultStart = { x: 100, y: 100 },
    mode = 'full', // 新增模式参数
  } = options

  if (!Array.isArray(steps)) {
    console.warn('convertRecipeStepsToNodes: steps should be an array')
    return []
  }

  // 过滤掉无效的步骤数据
  const validSteps = steps.filter((step, index) => {
    if (!step || typeof step !== 'object') {
      console.warn(`Step at index ${index} is not a valid object:`, step)
      return false
    }

    // 检查 id 是否存在且有效（用作 recipeStepId）
    if (step.id === null || step.id === undefined) {
      console.warn(`Step at index ${index} has no valid id:`, step)
      return false
    }

    // 检查 orderIndex 是否存在且有效
    if (step.orderIndex === null || step.orderIndex === undefined) {
      console.warn(`Step at index ${index} has no valid orderIndex:`, step)
      return false
    }

    return true
  })

  console.log('Valid steps for conversion:', validSteps)

  return validSteps.map((step, index) => {
    // 使用 recipeStepId (即step.id) 作为节点 ID
    const stepId = `step-${step.id}`

    // 位置计算：优先使用数据库中的位置，否则基于 orderIndex 自动排列
    let x = defaultStart.x
    let y = defaultStart.y

    // 安全地获取位置
    if (step.positionX !== null && step.positionX !== undefined && !isNaN(step.positionX)) {
      x = Number(step.positionX)
    } else {
      // 自动布局：3列排列
      const orderIndex = Number(step.orderIndex)
      x = defaultStart.x + (orderIndex % 3) * defaultSpacing.x
      y = defaultStart.y + Math.floor(orderIndex / 3) * defaultSpacing.y
    }

    if (step.positionY !== null && step.positionY !== undefined && !isNaN(step.positionY)) {
      y = Number(step.positionY)
    }

    // 安全地获取其他属性
    const orderIndex = Number(step.orderIndex)
    const title = step.title || '未命名步骤'
    const note = step.note || ''
    const estMinutes =
      step.estMinutes !== null && step.estMinutes !== undefined ? Number(step.estMinutes) : null
    const parallelizable = Boolean(step.parallelizable)

    // 格式化标题为 "Step1.准备食材" 格式
    const formattedLabel = `Step${orderIndex + 1}.${title}`

    // 根据是否可并行选择不同的橙色系配色
    const nodeStyle = parallelizable
      ? {
          // 可并行步骤 - 明亮橙色系
          backgroundColor: '#fff7ed', // orange-50
          border: '2px solid #fed7aa', // orange-200
          borderRadius: '12px',
          width: '220px',
          minHeight: '100px',
          boxShadow:
            '0 4px 6px -1px rgba(251, 146, 60, 0.1), 0 2px 4px -1px rgba(251, 146, 60, 0.06)',
          color: '#9a3412', // orange-800
        }
      : {
          // 顺序步骤 - 深橙色系
          backgroundColor: '#ffedd5', // orange-100
          border: '2px solid #fb923c', // orange-400
          borderRadius: '12px',
          width: '220px',
          minHeight: '100px',
          boxShadow:
            '0 4px 6px -1px rgba(234, 88, 12, 0.1), 0 2px 4px -1px rgba(234, 88, 12, 0.06)',
          color: '#7c2d12', // orange-900
        }

    return {
      id: stepId, // 使用 step-{recipeStepId} 格式
      type: nodeType,
      position: { x, y },
      data: {
        // 显示用的标签 - 格式化为 Step1.标题
        label: formattedLabel,

        // 步骤详细信息
        id: step.id, // recipeStepId
        title: title,
        note: note,
        orderIndex: orderIndex,
        estMinutes: estMinutes,
        parallelizable: parallelizable,
        recipeId: step.recipeId,

        // 保留完整的原始数据
        originalStep: { ...step },

        // 用于节点样式的辅助属性
        isParallel: parallelizable,
        hasNote: Boolean(note),
        duration: estMinutes || 0,

        // 格式化的显示文本
        formattedTitle: formattedLabel,
        displayNote: note || '暂无备注',
        durationText: estMinutes ? `${estMinutes}分钟` : '时间待定',
      },

      // 节点样式 - 橙色系
      style: nodeStyle,

      // 可选：添加类名用于CSS样式
      class: [
        'recipe-step-node',
        parallelizable ? 'parallel-step' : 'sequential-step',
        estMinutes ? 'has-duration' : 'no-duration',
        note ? 'has-note' : 'no-note',
      ].join(' '),
    }
  })
}

/**
 * 生成连接线（边）- 显示耗时信息
 * @param {Array} nodes - VueFlow 节点数组
 * @returns {Array} VueFlow 边数组
 */
export const generateStepEdges = (nodes) => {
  if (!Array.isArray(nodes) || nodes.length < 2) {
    return []
  }

  const edges = []

  // 按 orderIndex 排序
  const sortedNodes = [...nodes]
    .filter((node) => node && node.data && node.data.orderIndex !== undefined)
    .sort((a, b) => {
      return Number(a.data.orderIndex) - Number(b.data.orderIndex)
    })

  for (let i = 0; i < sortedNodes.length - 1; i++) {
    const currentNode = sortedNodes[i]
    const nextNode = sortedNodes[i + 1]

    // 计算连线上显示的耗时信息
    const currentDuration = currentNode.data.estMinutes || 0
    const edgeLabel = currentDuration > 0 ? `${currentDuration}分钟` : '时间待定'

    edges.push({
      id: `edge-${currentNode.data.orderIndex}-${nextNode.data.orderIndex}`,
      source: currentNode.id, // step-0, step-1 等
      target: nextNode.id,
      type: 'smoothstep',
      animated: false,
      style: {
        stroke: '#fb923c', // orange-400
        strokeWidth: 3,
        strokeDasharray: currentNode.data.isParallel ? '5,5' : undefined, // 并行步骤使用虚线
      },
      label: edgeLabel, // 显示耗时而不是步骤序号
      labelStyle: {
        fontSize: '12px',
        fontWeight: 600,
        color: '#9a3412', // orange-800
        backgroundColor: '#fff7ed', // orange-50
        padding: '2px 6px',
        borderRadius: '4px',
        border: '1px solid #fed7aa', // orange-200
      },
      labelBgPadding: [8, 4],
      labelBgBorderRadius: 4,
      labelBgStyle: {
        fill: '#fff7ed', // orange-50
        fillOpacity: 0.9,
      },
      // 标签位置 - 在连线的中间偏上方
      labelShowBg: true,
      labelBgStyle: {
        fill: '#fff7ed',
        fillOpacity: 0.9,
        stroke: '#fed7aa',
        strokeWidth: 1,
      },
    })
  }

  return edges
}

/**
 * 生成自动布局的节点位置
 * @param {Array} nodes - VueFlow 节点数组
 * @param {Object} layoutOptions - 布局选项
 * @returns {Array} 更新位置后的节点数组
 */
export const autoLayoutNodes = (nodes, layoutOptions = {}) => {
  const {
    type = 'flow', // 'grid' | 'flow' | 'cascade'
    spacing = { x: 280, y: 200 }, // 增加间距
    start = { x: 120, y: 120 },
    columns = 3,
  } = layoutOptions

  const sortedNodes = [...nodes].sort(
    (a, b) => (a.data?.orderIndex || 0) - (b.data?.orderIndex || 0),
  )

  return sortedNodes.map((node, index) => {
    let newPosition = { ...node.position }

    switch (type) {
      case 'grid':
        newPosition = {
          x: start.x + (index % columns) * spacing.x,
          y: start.y + Math.floor(index / columns) * spacing.y,
        }
        break

      case 'flow':
        // 流式布局 - 水平排列，适当错开
        newPosition = {
          x: start.x + index * spacing.x,
          y: start.y + (index % 2) * (spacing.y * 0.3), // 轻微交错
        }
        break

      case 'cascade':
        // 瀑布式布局
        newPosition = {
          x: start.x + index * (spacing.x * 0.8),
          y: start.y + index * (spacing.y * 0.5),
        }
        break
    }

    return {
      ...node,
      position: newPosition,
    }
  })
}

// 其他函数保持不变...
export const convertNodesToRecipeSteps = (nodes) => {
  if (!Array.isArray(nodes)) {
    console.warn('convertNodesToRecipeSteps: nodes should be an array')
    return []
  }

  return nodes
    .filter((node) => {
      if (!node || !node.data) {
        console.warn('Invalid node found:', node)
        return false
      }
      return true
    })
    .map((node) => {
      const originalStep = node.data?.originalStep || {}

      // 从节点数据中获取 recipeStepId（优先使用node.data.id）
      const recipeStepId = node.data?.id || 
        (node.id.startsWith('step-') ? parseInt(node.id.replace('step-', '')) : null)

      // 从节点数据中获取 orderIndex
      const orderIndex = node.data?.orderIndex || originalStep.orderIndex || 0

      // 安全地转换位置
      const positionX = node.position?.x !== undefined ? Number(node.position.x).toFixed(2) : '0.00'
      const positionY = node.position?.y !== undefined ? Number(node.position.y).toFixed(2) : '0.00'

      return {
        ...originalStep,
        // 确保 ID 正确（这是 recipeStepId）
        id: recipeStepId,
        // 确保 orderIndex 正确
        orderIndex: orderIndex,
        // 更新位置信息
        positionX: positionX,
        positionY: positionY,

        // 如果节点数据中有更新的信息，也要同步
        title: node.data?.title || originalStep.title,
        note: node.data?.note || originalStep.note,
        estMinutes: node.data?.estMinutes || originalStep.estMinutes,
        parallelizable: node.data?.parallelizable ? 1 : 0,
        recipeId: node.data?.recipeId || originalStep.recipeId,
      }
    })
}

export const findNodeByOrderIndex = (nodes, orderIndex) => {
  return nodes.find((node) => node.data?.orderIndex === orderIndex) || null
}

export const findNodeByRecipeStepId = (nodes, recipeStepId) => {
  return nodes.find((node) => node.data?.id === recipeStepId) || null
}

export const batchUpdatePositionsByOrderIndex = async (nodes, updateAPI) => {
  const updates = convertNodesToRecipeSteps(nodes).map((step) => ({
    orderIndex: step.orderIndex,
    positionX: step.positionX,
    positionY: step.positionY,
    recipeId: step.recipeId,
  }))

  try {
    const results = await Promise.allSettled(
      updates.map((update) =>
        updateAPI(update.orderIndex, {
          positionX: update.positionX,
          positionY: update.positionY,
          recipeId: update.recipeId,
        }),
      ),
    )

    const failures = results.filter((result) => result.status === 'rejected')
    if (failures.length > 0) {
      console.warn('Some position updates failed:', failures)
    }

    return {
      success: results.length - failures.length,
      failed: failures.length,
      total: results.length,
    }
  } catch (error) {
    console.error('Batch update failed:', error)
    throw error
  }
}

export const debugStepsData = (steps) => {
  console.group('🔍 Debug Recipe Steps Data')
  console.log('Steps array:', steps)
  console.log('Is array:', Array.isArray(steps))
  console.log('Length:', steps?.length)

  if (Array.isArray(steps)) {
    steps.forEach((step, index) => {
      console.log(`Step ${index}:`, {
        orderIndex: step?.orderIndex,
        title: step?.title,
        note: step?.note,
        estMinutes: step?.estMinutes,
        positionX: step?.positionX,
        positionY: step?.positionY,
        parallelizable: step?.parallelizable,
        recipeId: step?.recipeId,
        fullObject: step,
      })
    })
  }
  console.groupEnd()
}
