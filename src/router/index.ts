import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import ACCESS_ENUM from '@/access/accessEnum'

/**
 * 路由配置
 * 基于HTML原型设计的完整路由结构
 */
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/HomePage.vue'),
    meta: {
      title: '知食煮意 - 个性化食谱知识库',
      access: ACCESS_ENUM.NOT_LOGIN,
    },
  },

  // 登录后默认重定向到 dashboard
  {
    path: '/app',
    redirect: '/dashboard',
  },

  // 认证相关路由
  {
    path: '/auth',
    redirect: '/user/login',
    children: [],
  },
  {
    path: '/user',
    children: [
      {
        path: 'login',
        name: 'Login',
        component: () => import('@/views/auth/LoginPage.vue'),
        meta: {
          title: '用户登录',
          access: ACCESS_ENUM.NOT_LOGIN,
          hideInMenu: true,
        },
      },
      // {
      //   path: 'register',
      //   name: 'Register',
      //   component: () => import('@/views/auth/RegisterPage.vue'),
      //   meta: {
      //     title: '用户注册',
      //     access: ACCESS_ENUM.NOT_LOGIN,
      //     hideInMenu: true
      //   }
      // }
    ],
  },

  // 主应用路由（需要登录）
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/layouts/AppLayout.vue'),
    meta: {
      title: '用户仪表板',
      access: ACCESS_ENUM.USER,
      icon: 'home',
    },
    children: [
      {
        path: '',
        component: () => import('@/views/user/UserDashboardPage.vue'),
      },
    ],
  },
  {
    path: '/recipegallery',
    name: 'Recipes',
    component: () => import('@/layouts/AppLayout.vue'),
    meta: {
      title: '食谱浏览',
      access: ACCESS_ENUM.USER,
      icon: 'book',
    },
    children: [
      {
        path: '',
        component: () => import('@/views/recipe/RecipeGallery.vue'),
      },
    ],
  },
  {
    path: '/recipe/:id',
    name: 'RecipeDetail',
    component: () => import('@/layouts/AppLayout.vue'),
    meta: {
      title: '食谱详情',
      access: ACCESS_ENUM.USER,
      hideInMenu: true,
    },
    children: [
      {
        path: '',
        component: () => import('@/views/recipe/RecipeDetailPage.vue'),
      },
    ],
  },
  {
    path: '/components-demo',
    name: 'ComponentsDemo',
    component: () => import('@/layouts/AppLayout.vue'),
    meta: {
      title: '组件展示',
      access: ACCESS_ENUM.USER,
      hideInMenu: true,
    },
    children: [
      {
        path: '',
        component: () => import('@/views/ComponentsDemo.vue'),
      },
    ],
  },

  // 错误页面
  // {
  //   path: '/403',
  //   name: 'NoAuth',
  //   component: () => import('@/views/error/NoAuthPage.vue'),
  //   meta: {
  //     title: '无访问权限',
  //     access: ACCESS_ENUM.NOT_LOGIN,
  //     hideInMenu: true
  //   }
  // },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/NotFoundPage.vue'),
    meta: {
      title: '页面不存在',
      access: ACCESS_ENUM.NOT_LOGIN,
      hideInMenu: true,
    },
  },

  // 通配符路由 - 必须放在最后
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404',
  },
]

/**
 * 创建路由器实例
 */
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  // 路由切换时滚动到顶部
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    return { top: 0 }
  },
})

/**
 * 路由元信息类型扩展
 */
declare module 'vue-router' {
  interface RouteMeta {
    /** 页面标题 */
    title?: string
    /** 访问权限 */
    access?: string
    /** 是否在菜单中隐藏 */
    hideInMenu?: boolean
    /** 菜单图标 */
    icon?: string
    /** 是否缓存页面 */
    keepAlive?: boolean
  }
}

// 全局前置守卫 - 设置页面标题
router.beforeEach((to) => {
  if (to.meta.title) {
    document.title = to.meta.title
  }
})

export default router

/**
 * 获取菜单路由
 * 过滤掉不需要在菜单中显示的路由
 */
export function getMenuRoutes(): RouteRecordRaw[] {
  const filterRoutes = (routes: RouteRecordRaw[]): RouteRecordRaw[] => {
    return routes
      .filter((route) => !route.meta?.hideInMenu)
      .map((route) => ({
        ...route,
        children: route.children ? filterRoutes(route.children) : [],
      }))
  }

  return filterRoutes(routes)
}

/**
 * 根据权限过滤路由
 */
export function filterRoutesByAccess(
  routes: RouteRecordRaw[],
  userAccess: string,
): RouteRecordRaw[] {
  return routes.filter((route) => {
    const routeAccess = route.meta?.access || ACCESS_ENUM.NOT_LOGIN

    // 检查权限逻辑
    if (routeAccess === ACCESS_ENUM.NOT_LOGIN) return true
    if (routeAccess === ACCESS_ENUM.USER && userAccess !== ACCESS_ENUM.NOT_LOGIN) return true
    if (routeAccess === ACCESS_ENUM.ADMIN && userAccess === ACCESS_ENUM.ADMIN) return true

    return false
  })
}
