<template>
  <div id="recipe-card">
    <a-card
      :hoverable="!loading && hoverable"
      :loading="loading"
      :class="[
        'recipe-card',
        `recipe-card--${size}`,
        `recipe-card--${layout}`,
        {
          'recipe-card--favorited': isFavorited,
          'recipe-card--featured': featured,
        },
      ]"
      @click="handleCardClick"
    >
      <!-- 卡片封面 -->
      <template #cover>
        <div class="recipe-cover">
          <!-- 食谱图片 -->
          <!--src后续需要更改-->
          <div class="cover-image-container">
            <a-image
              :src="recipe.coverKey ? defaultCoverUrl : defaultCoverUrl"
              :alt="recipe.name"
              :fallback="defaultCoverUrl"
              class="cover-image"
              @error="handleImageError"
            />

            <!-- 图片遮罩层 -->
            <div class="image-overlay">
              <!-- 操作按钮组 -->
              <div v-if="showActions" class="action-buttons">
                <a-tooltip title="收藏">
                  <a-button
                    type="text"
                    shape="circle"
                    :icon="h(isFavorited ? HeartFilled : HeartOutlined)"
                    :class="['action-btn', 'favorite-btn', { 'favorite-btn--active': isFavorited }]"
                    @click.stop="handleToggleFavorite"
                  />
                </a-tooltip>

                <a-tooltip title="分享">
                  <a-button
                    type="text"
                    shape="circle"
                    :icon="h(ShareAltOutlined)"
                    class="action-btn share-btn"
                    @click.stop="handleShare"
                  />
                </a-tooltip>

                <a-tooltip v-if="showEditAction" title="编辑">
                  <a-button
                    type="text"
                    shape="circle"
                    :icon="h(EditOutlined)"
                    class="action-btn edit-btn"
                    @click.stop="handleEdit"
                  />
                </a-tooltip>
              </div>
            </div>
          </div>

          <!-- 特色标签 -->
          <div v-if="featured" class="featured-badge">
            <i class="fa fa-star"></i>
          </div>

          <!-- 价格标签 暂不实现 -->
          <!-- <div v-if="recipe.estimatedCost" class="price-tag">
          ¥{{ recipe.estimatedCost }}
        </div> -->
        </div>
      </template>

      <!-- 卡片内容 -->
      <a-card-meta class="recipe-meta">
        <template #title>
          <a-tooltip :title="recipe.name">
            <h3 class="recipe-title">{{ recipe.name }}</h3>
          </a-tooltip>
        </template>

        <template #description>
          <div class="recipe-info">
            <!-- 食谱描述 -->
            <p v-if="recipe.introduction" class="recipe-description">
              {{ truncateText(recipe.introduction, descriptionMaxLength) }}
            </p>

            <!-- 标签列表 -->
            <div v-if="recipe.tags && recipe.tags.length > 0" class="recipe-tags">
              <a-tag
                v-for="tag in displayTags"
                :key="tag"
                :color="getTagColor(tag)"
                class="recipe-tag"
                @click.stop="handleTagClick(tag)"
              >
                {{ tag }}
              </a-tag>
            </div>

            <!-- 统计信息 -->
            <div class="recipe-stats">
              <!-- <div class="stat-item">
              <ClockCircleOutlined class="stat-icon" />
              <span class="stat-text">{{ formatCookTime(recipe.cookTime) }}</span>
            </div>
            
            <div class="stat-item">
              <UserOutlined class="stat-icon" />
              <span class="stat-text">{{ recipe.servings || 1 }}人份</span>
            </div> -->

              <!-- <div v-if="recipe.difficulty" class="stat-item difficulty" :class="`difficulty--${recipe.difficulty}`">
              <TrophyOutlined class="stat-icon" />
              <span class="stat-text">{{ getDifficultyText(recipe.difficulty) }}</span>
            </div> -->

              <div v-if="recipe.stepCount" class="stat-item">
                <OrderedListOutlined class="stat-icon" />
                <span class="stat-text">{{ recipe.stepCount }}步</span>
              </div>
            </div>

            <!-- 评分和互动数据 -->
            <!-- <div class="recipe-rating">
            <a-rate 
              :value="recipe.rating || 0" 
              disabled 
              :count="5"
              class="rating-stars"
              allow-half
            />
            <span class="rating-text">{{ formatRating(recipe.rating) }}</span>
            
            <div class="interaction-stats">
              <span v-if="recipe.viewCount" class="stat-badge">
                <EyeOutlined />
                {{ formatNumber(recipe.viewCount) }}
              </span>
              <span v-if="recipe.favoriteCount" class="stat-badge">
                <HeartOutlined />
                {{ formatNumber(recipe.favoriteCount) }}
              </span>
            </div>
          </div> -->
          </div>
        </template>
      </a-card-meta>

      <!-- 卡片底部操作区 -->
      <template v-if="showFooterActions" #actions>
        <a-tooltip title="查看详情">
          <EyeOutlined @click.stop="handleView" />
        </a-tooltip>

        <a-tooltip title="添加到收藏">
          <HeartOutlined @click.stop="handleToggleFavorite" />
        </a-tooltip>

        <a-tooltip title="分享食谱">
          <ShareAltOutlined @click.stop="handleShare" />
        </a-tooltip>
      </template>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { computed, h, ref } from 'vue'
import {
  HeartOutlined,
  HeartFilled,
  ShareAltOutlined,
  EditOutlined,
  ClockCircleOutlined,
  UserOutlined,
  TrophyOutlined,
  OrderedListOutlined,
  EyeOutlined,
} from '@ant-design/icons-vue'
import defaultCoverUrlPath from '@/assest/images/DefaultImage.jpg'
import type { RecipeVO } from '@/api/recipe/model'
import router from '@/router'
/**
 * 食谱卡片组件
 *
 * @description 可复用的食谱展示卡片，支持多种布局和交互方式
 * <AUTHOR> Way Team
 */

// interface Recipe {
//   id?: number
//   name?: string
//   introduction?: string
//   coverKey?: string
//   tags?: string[]
//   category?: string
//   stepCount?: number
//   cookTime?: number
//   servings?: number
//   difficulty?: 'easy' | 'medium' | 'hard'
//   rating?: number
//   viewCount?: number
//   favoriteCount?: number
//   estimatedCost?: number
//   createTime?: string
//   updateTime?: string
// }

interface Props {
  /** 食谱数据 */
  recipe: API.RecipeVO
  /** 卡片尺寸 */
  size?: 'small' | 'medium' | 'large'
  /** 布局方式 */
  layout?: 'vertical' | 'horizontal'
  /** 是否可悬停 */
  hoverable?: boolean
  /** 是否加载中 */
  loading?: boolean
  /** 是否显示操作按钮 */
  showActions?: boolean
  /** 是否显示底部操作 */
  showFooterActions?: boolean
  /** 是否显示编辑按钮 */
  showEditAction?: boolean
  /** 是否为特色推荐 */
  featured?: boolean
  /** 是否已收藏 */
  isFavorited?: boolean
  /** 是否预览图片 */
  previewImage?: boolean
  /** 描述文字最大长度 */
  descriptionMaxLength?: number
  /** 最大显示标签数 */
  maxTagsDisplay?: number
}

interface Emits {
  /** 卡片点击事件 */
  (e: 'click', recipe: RecipeVO): void
  /** 收藏切换事件 */
  (e: 'toggle-favorite', recipeId: number, isFavorited: boolean): void
  /** 分享事件 */
  (e: 'share', recipe: RecipeVO): void
  /** 编辑事件 */
  (e: 'edit', recipe: RecipeVO): void
  /** 查看详情事件 */
  (e: 'view', recipe: RecipeVO): void
  /** 标签点击事件 */
  (e: 'tag-click', tag: string): void
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  layout: 'vertical',
  hoverable: true,
  loading: false,
  showActions: true,
  showFooterActions: false,
  showEditAction: false,
  featured: false,
  isFavorited: false,
  previewImage: false,
  descriptionMaxLength: 100,
  maxTagsDisplay: 3,
})

const emit = defineEmits<Emits>()

// 计算属性
const defaultCoverUrl = ref<string>(defaultCoverUrlPath)

const displayTags = computed(() => {
  if (!props.recipe.tags) return []
  return props.recipe.tags.slice(0, props.maxTagsDisplay)
})

// 工具函数
// const getCoverUrl = (coverKey: string): string => {
//   // TODO: 根据实际的文件服务配置
//   return `https://your-cdn.com/${coverKey}`
// }

const truncateText = (text: string, maxLength: number): string => {
  if (!text) return ''
  return text.length > maxLength ? `${text.slice(0, maxLength)}...` : text
}

const formatCookTime = (minutes?: number): string => {
  if (!minutes) return '未知'
  if (minutes < 60) return `${minutes}分钟`
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`
}

const getDifficultyText = (difficulty: string): string => {
  const difficultyMap = {
    easy: '简单',
    medium: '中等',
    hard: '困难',
  }
  return difficultyMap[difficulty as keyof typeof difficultyMap] || '未知'
}

const formatRating = (rating?: number): string => {
  if (!rating) return '暂无评分'
  return rating.toFixed(1)
}

const formatNumber = (num?: number): string => {
  if (!num) return '0'
  if (num < 1000) return num.toString()
  if (num < 10000) return `${(num / 1000).toFixed(1)}k`
  return `${(num / 10000).toFixed(1)}w`
}

const getTagColor = (tag: string): string => {
  const colorMap: Record<string, string> = {
    川菜: 'red',
    粤菜: 'green',
    素食: 'blue',
    快手: 'orange',
    烘焙: 'purple',
    家常: 'cyan',
    下饭: 'gold',
  }
  return colorMap[tag] || 'default'
}

// 事件处理器
const handleCardClick = () => {
  if (!props.loading) {
    console.log('跳转', props.recipe.id)
    router.push({
      path: `/recipe/${props.recipe.id}`,
      // path: '/recipe',
    })
  }
}

const handleToggleFavorite = () => {
  if (props.recipe.id) {
    emit('toggle-favorite', props.recipe.id, !props.isFavorited)
  }
}

const handleShare = () => {
  emit('share', props.recipe)
}

const handleEdit = () => {
  emit('edit', props.recipe)
}

const handleView = () => {
  emit('view', props.recipe)
}

const handleTagClick = (tag: string) => {
  emit('tag-click', tag)
}

const handleImageError = () => {
  console.warn('食谱图片加载失败:', props.recipe.coverKey)
}
</script>

<style lang="less" scoped>
.recipe-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #f0f0f0;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
    border-color: #ff7a00;
  }

  &--featured {
    border-color: #ff7a00;
    background: linear-gradient(135deg, #fff 0%, #fff9f0 100%);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, #ff7a00 0%, #ff9500 100%);
      z-index: 1;
    }
  }

  :deep(.ant-card-body) {
    padding: 16px;
  }

  :deep(.ant-card-actions) {
    border-top: 1px solid #f0f0f0;
    background: #fafafa;

    li {
      margin: 8px 0;

      .anticon {
        font-size: 16px;
        color: #666;
        transition: color 0.3s;

        &:hover {
          color: #ff7a00;
        }
      }
    }
  }

  // 布局变体
  &--horizontal {
    :deep(.ant-card-cover) {
      float: left;
      width: 40%;
      margin-right: 16px;
    }

    :deep(.ant-card-body) {
      overflow: hidden;
    }
  }

  // 尺寸变体
  &--small {
    .recipe-cover {
      height: 140px;
    }

    .recipe-meta .recipe-title {
      font-size: 14px;
    }

    .recipe-stats .stat-item {
      font-size: 11px;
    }
  }

  &--medium {
    .recipe-cover {
      height: 180px;
    }
  }

  &--large {
    .recipe-cover {
      height: 220px;
    }

    .recipe-meta .recipe-title {
      font-size: 18px;
    }
  }
}

.recipe-cover {
  position: relative;
  overflow: hidden;

  .cover-image-container {
    position: relative;
    height: 100%;

    .cover-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    .image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.3);
      opacity: 0;
      transition: opacity 0.3s ease;

      .action-buttons {
        position: absolute;
        top: 12px;
        right: 12px;
        display: flex;
        flex-direction: column;
        gap: 8px;

        .action-btn {
          width: 32px;
          height: 32px;
          background: rgba(255, 255, 255, 0.9);
          border: none;
          backdrop-filter: blur(4px);

          &:hover {
            background: #fff;
            color: #ff7a00;
          }

          &.favorite-btn--active {
            color: #ff4d4f;
          }
        }
      }
    }

    &:hover {
      .cover-image {
        transform: scale(1.05);
      }

      .image-overlay {
        opacity: 1;
      }
    }
  }

  .featured-badge {
    position: absolute;
    top: 12px;
    left: 12px;
    background: linear-gradient(135deg, #ff7a00 0%, #ff9500 100%);
    color: #fff;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;

    i {
      font-size: 10px;
    }
  }

  .price-tag {
    position: absolute;
    bottom: 12px;
    right: 12px;
    background: rgba(255, 122, 0, 0.9);
    color: #fff;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;
    backdrop-filter: blur(4px);
  }
}

.recipe-meta {
  .recipe-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 8px;
    line-height: 1.4;
    color: #1a1a1a;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}

.recipe-info {
  .recipe-description {
    font-size: 13px;
    color: #666;
    line-height: 1.5;
    margin: 0 0 12px;
  }

  .recipe-tags {
    margin-bottom: 12px;

    .recipe-tag {
      font-size: 11px;
      margin: 0 4px 4px 0;
      border-radius: 4px;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .recipe-stats {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    flex-wrap: wrap;

    .stat-item {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #666;

      .stat-icon {
        margin-right: 4px;
        font-size: 11px;
      }

      &.difficulty {
        font-weight: 500;

        &--easy {
          color: #52c41a;
        }

        &--medium {
          color: #faad14;
        }

        &--hard {
          color: #ff4d4f;
        }
      }
    }
  }

  .recipe-rating {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .rating-stars {
      font-size: 12px;
      margin-right: 8px;
    }

    .rating-text {
      font-size: 12px;
      color: #666;
      font-weight: 500;
    }

    .interaction-stats {
      display: flex;
      gap: 8px;

      .stat-badge {
        display: flex;
        align-items: center;
        font-size: 11px;
        color: #999;

        .anticon {
          margin-right: 2px;
          font-size: 10px;
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .recipe-card {
    &--large .recipe-cover {
      height: 160px;
    }

    .recipe-cover .cover-image-container .image-overlay .action-buttons {
      top: 8px;
      right: 8px;

      .action-btn {
        width: 28px;
        height: 28px;
      }
    }

    .recipe-stats {
      gap: 8px;

      .stat-item {
        font-size: 11px;
      }
    }
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .recipe-card {
    background: #1f1f1f;
    border-color: #333;

    .recipe-meta .recipe-title {
      color: #fff;
    }

    .recipe-info {
      .recipe-description {
        color: #ccc;
      }

      .recipe-stats .stat-item {
        color: #999;
      }

      .recipe-rating {
        .rating-text {
          color: #ccc;
        }

        .interaction-stats .stat-badge {
          color: #999;
        }
      }
    }
  }
}
</style>
