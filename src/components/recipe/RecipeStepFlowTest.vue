<template>
  <div class="test-container">
    <h2>RecipeStepFlow 预览模式测试</h2>
    
    <div class="mode-selector">
      <a-radio-group v-model="currentMode" @change="handleModeChange">
        <a-radio-button value="full">完整模式</a-radio-button>
        <a-radio-button value="preview">预览模式</a-radio-button>
        <a-radio-button value="compact">紧凑模式</a-radio-button>
      </a-radio-group>
    </div>

    <div class="flow-container">
      <RecipeStepFlow
        :recipeList="mockRecipeSteps"
        :mode="currentMode"
        :height="currentMode === 'preview' ? '250px' : '400px'"
        :showToolbar="currentMode === 'full'"
        :editable="currentMode === 'full'"
        @nodeClick="handleNodeClick"
        @nodeUpdate="handleNodeUpdate"
        @positionChange="handlePositionChange"
        @error="handleFlowError"
      />
    </div>

    <div class="info-panel">
      <h3>当前配置：</h3>
      <ul>
        <li>模式: {{ currentMode }}</li>
        <li>步骤数量: {{ mockRecipeSteps.length }}</li>
        <li>高度: {{ currentMode === 'preview' ? '250px' : '400px' }}</li>
        <li>可编辑: {{ currentMode === 'full' ? '是' : '否' }}</li>
        <li>显示工具栏: {{ currentMode === 'full' ? '是' : '否' }}</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import RecipeStepFlow from './RecipeStepFlow.vue'

const currentMode = ref<'full' | 'preview' | 'compact'>('preview')

// 模拟菜谱步骤数据
const mockRecipeSteps = ref([
  {
    orderIndex: 0,
    title: "准备食材",
    note: "洗净所有蔬菜，切成合适大小",
    estMinutes: 10,
    parallelizable: 0,
    positionX: null,
    positionY: null,
    recipeId: 1
  },
  {
    orderIndex: 1,
    title: "加热锅子",
    note: "使用中火加热平底锅",
    estMinutes: 5,
    parallelizable: 1,
    positionX: null,
    positionY: null,
    recipeId: 1
  },
  {
    orderIndex: 2,
    title: "炒制蔬菜",
    note: "先放入较硬的蔬菜，后放入容易熟的蔬菜",
    estMinutes: 8,
    parallelizable: 0,
    positionX: null,
    positionY: null,
    recipeId: 1
  },
  {
    orderIndex: 3,
    title: "调味装盘",
    note: "添加适量盐和胡椒粉，装盘上桌",
    estMinutes: 3,
    parallelizable: 0,
    positionX: null,
    positionY: null,
    recipeId: 1
  }
])

const handleModeChange = (e: any) => {
  console.log('Mode changed to:', e.target.value)
}

const handleNodeClick = (node: any) => {
  console.log('Node clicked:', node)
  message.info(`点击了节点: ${node.title}`)
}

const handleNodeUpdate = (node: any) => {
  console.log('Node updated:', node)
}

const handlePositionChange = (nodes: any[]) => {
  console.log('Position changed:', nodes)
  message.success('位置已更新')
}

const handleFlowError = (error: string) => {
  console.error('Flow error:', error)
  message.error(error)
}
</script>

<style scoped>
.test-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.mode-selector {
  margin: 20px 0;
  text-align: center;
}

.flow-container {
  margin: 20px 0;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.info-panel {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.info-panel h3 {
  margin-top: 0;
  color: #ff7a00;
}

.info-panel ul {
  margin: 0;
  padding-left: 20px;
}

.info-panel li {
  margin: 8px 0;
  color: #666;
}
</style>
