<template>
  <div
    class="recipe-step-node-content"
    :class="[
      data.isParallel ? 'parallel-step' : 'sequential-step',
      data.hasNote ? 'has-note' : 'no-note',
      selected ? 'selected' : '',
    ]"
    :data-mode="mode"
    @click="handleClick"
    @mousedown.prevent
  >
    <!-- 标题区域 -->
    <div class="step-header">
      <h4 class="step-title" :title="data.formattedTitle">
        {{ data.formattedTitle }}
      </h4>
      <div class="step-meta">
        <span v-if="data.estMinutes" class="duration-badge">
          {{ data.durationText }}
        </span>
        <span v-if="data.parallelizable" class="parallel-badge"> 可并行 </span>
      </div>
    </div>

    <!-- 备注区域 - 简化结构，移除冗余div -->
    <div v-if="shouldShowNote" class="step-note">
      <p :title="stepNote">{{ stepNote }}</p>
    </div>

    <!-- 无备注时的占位（仅在完整模式下显示） - 简化结构 -->
    <div v-else-if="mode === 'full'" class="step-note-placeholder">
      <p class="text-placeholder">暂无备注</p>
    </div>

    <!-- 步骤序号指示器 -->
    <div class="step-indicator">
      {{ data.orderIndex + 1 }}
    </div>

    <!-- 编辑状态指示器 -->
    <div v-if="editable" class="edit-indicator">
      <EditOutlined />
    </div>
  </div>
</template>

<script setup lang="ts" name="RecipeStepNode">
import { computed } from 'vue'
import { EditOutlined } from '@ant-design/icons-vue'

// Props 定义
interface Props {
  data: {
    label: string
    title: string
    note: string
    orderIndex: number
    estMinutes: number | null
    parallelizable: boolean
    recipeId?: number
    originalStep: any
    isParallel: boolean
    hasNote: boolean
    duration: number
    formattedTitle: string
    displayNote: string
    durationText: string
  }
  selected?: boolean
  editable?: boolean
  mode?: 'full' | 'preview' | 'compact' // 新增模式支持
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  editable: false,
  mode: 'full',
})

// Emits 定义
const emit = defineEmits<{
  update: [node: any]
  click: [data: any]
}>()

// 计算属性
const stepNote = computed(() => {
  return props.data.displayNote || props.data.note || '暂无备注'
})

// 根据模式计算样式配置 - 统一preview和full模式的视觉样式
const modeStyles = computed(() => {
  switch (props.mode) {
    case 'preview':
      return {
        width: '220px', // 与full模式保持一致
        minHeight: '100px', // 与full模式保持一致
        padding: '12px', // 与full模式保持一致
        titleFontSize: '14px', // 与full模式保持一致
        noteFontSize: '12px', // 与full模式保持一致
        badgeFontSize: '10px', // 与full模式保持一致
        indicatorSize: '24px', // 与full模式保持一致
        showNote: true, // 显示备注，与full模式保持一致
        maxTitleLines: 2,
      }
    case 'compact':
      return {
        width: '180px',
        minHeight: '80px',
        padding: '10px',
        titleFontSize: '13px',
        noteFontSize: '11px',
        badgeFontSize: '9px',
        indicatorSize: '20px',
        showNote: true,
        maxTitleLines: 2,
      }
    default: // 'full'
      return {
        width: '220px',
        minHeight: '100px',
        padding: '12px',
        titleFontSize: '14px',
        noteFontSize: '12px',
        badgeFontSize: '10px',
        indicatorSize: '24px',
        showNote: true,
        maxTitleLines: 2,
      }
  }
})

// 是否显示备注
const shouldShowNote = computed(() => {
  return modeStyles.value.showNote && props.data.hasNote
})

// 事件处理
const handleClick = (event: Event) => {
  event.preventDefault()
  event.stopPropagation()
  emit('click', props.data)
}
</script>

<style scoped>
.recipe-step-node-content {
  padding: v-bind('modeStyles.padding');
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  cursor: pointer;
  transition:
    all 0.3s ease,
    transform 0.2s ease;
  min-height: v-bind('modeStyles.minHeight');
  width: v-bind('modeStyles.width');
  background: #fff7ed; /* orange-50 */
  border: 2px solid #fed7aa; /* orange-200 */
  border-radius: 12px;
  box-shadow:
    0 4px 6px -1px rgba(251, 146, 60, 0.1),
    0 2px 4px -1px rgba(251, 146, 60, 0.06);
  color: #9a3412; /* orange-800 */
  /* 确保整个节点都能响应点击 */
  user-select: none;
}

/* 顺序步骤样式 */
.recipe-step-node-content.sequential-step {
  background: #ffedd5; /* orange-100 */
  border-color: #fb923c; /* orange-400 */
  color: #7c2d12; /* orange-900 */
  box-shadow:
    0 4px 6px -1px rgba(234, 88, 12, 0.1),
    0 2px 4px -1px rgba(234, 88, 12, 0.06);
}

/* 并行步骤样式 */
.recipe-step-node-content.parallel-step {
  background: #fff7ed; /* orange-50 */
  border-color: #fed7aa; /* orange-200 */
  color: #9a3412; /* orange-800 */
  border-style: dashed; /* 虚线边框表示可并行 */
}

/* 并行步骤选中状态 - 保持虚线样式 */
.recipe-step-node-content.parallel-step.selected {
  border-style: dashed !important; /* 确保选中时仍保持虚线 */
}

/* 选中状态 - 统一的选择样式，避免与Vue Flow冲突 */
.recipe-step-node-content.selected {
  border-color: #ff7a00 !important;
  border-width: 3px !important;
  /* 添加内阴影提供选中反馈，不与Vue Flow冲突 */
  box-shadow:
    inset 0 0 0 1px rgba(255, 122, 0, 0.3),
    0 4px 6px -1px rgba(251, 146, 60, 0.1),
    0 2px 4px -1px rgba(251, 146, 60, 0.06) !important;
  /* 移除transform，避免位移冲突 */
}

/* 悬停效果 - 恢复原始动画效果 */
.recipe-step-node-content:hover:not(.selected) {
  transform: translateY(-1px);
  box-shadow:
    0 8px 12px -2px rgba(251, 146, 60, 0.15),
    0 4px 8px -2px rgba(251, 146, 60, 0.1);
}

/* 选中状态的悬停效果 - 恢复动画并添加视觉反馈 */
.recipe-step-node-content.selected:hover {
  transform: translateY(-2px);
  border-color: #e65100 !important; /* 稍微深一点的橙色 */
  box-shadow:
    inset 0 0 0 1px rgba(230, 81, 0, 0.4),
    0 8px 16px -2px rgba(251, 146, 60, 0.2),
    0 4px 8px -2px rgba(251, 146, 60, 0.1) !important;
}

/* 标题区域 */
.step-header {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 8px;
  flex-shrink: 0;
  pointer-events: none; /* 禁用子元素的指针事件 */
}

.step-title {
  margin: 0;
  font-size: v-bind('modeStyles.titleFontSize');
  font-weight: 600;
  line-height: 1.3;
  color: inherit;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: v-bind('modeStyles.maxTitleLines');
  -webkit-box-orient: vertical;
}

.step-meta {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  align-items: center;
}

.duration-badge {
  background: #fb923c; /* orange-400 */
  color: white;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: v-bind('modeStyles.badgeFontSize');
  font-weight: 500;
  white-space: nowrap;
}

.parallel-badge {
  background: #fcd34d; /* yellow-300 */
  color: #92400e; /* yellow-800 */
  padding: 2px 6px;
  border-radius: 12px;
  font-size: v-bind('modeStyles.badgeFontSize');
  font-weight: 500;
  white-space: nowrap;
}

/* 备注区域 - 简化后的样式 */
.step-note {
  flex: 1;
  overflow: hidden;
  pointer-events: none; /* 禁用子元素的指针事件 */
}

.step-note p {
  margin: 0;
  font-size: v-bind('modeStyles.noteFontSize');
  line-height: 1.4;
  color: inherit;
  opacity: 0.8;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.step-note-placeholder {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none; /* 禁用子元素的指针事件 */
}

.text-placeholder {
  margin: 0;
  font-size: 11px;
  color: inherit;
  opacity: 0.5;
  font-style: italic;
}

/* 步骤序号指示器 */
.step-indicator {
  position: absolute;
  top: -8px;
  left: -8px;
  width: v-bind('modeStyles.indicatorSize');
  height: v-bind('modeStyles.indicatorSize');
  background: #ff7a00;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: calc(v-bind('modeStyles.indicatorSize') * 0.5);
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1;
  pointer-events: none; /* 禁用指针事件 */
}

/* 编辑指示器 */
.edit-indicator {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  background: #52c41a;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1;
  pointer-events: none; /* 禁用指针事件 */
}

/* 响应式设计 - 仅在compact模式下应用，保持preview和full模式的一致性 */
@media (max-width: 768px) {
  /* 只对compact模式应用响应式样式，preview和full模式保持原有尺寸 */
  .recipe-step-node-content[data-mode='compact'] {
    width: 160px;
    min-height: 70px;
    padding: 8px;
  }

  .recipe-step-node-content[data-mode='compact'] .step-title {
    font-size: 12px;
    -webkit-line-clamp: 2;
  }

  .recipe-step-node-content[data-mode='compact'] .step-note p {
    font-size: 10px;
    -webkit-line-clamp: 2;
  }

  .recipe-step-node-content[data-mode='compact'] .duration-badge,
  .recipe-step-node-content[data-mode='compact'] .parallel-badge {
    font-size: 8px;
    padding: 1px 4px;
  }

  .recipe-step-node-content[data-mode='compact'] .step-indicator {
    width: 18px;
    height: 18px;
    font-size: 9px;
  }
}

/* 动画效果 */
@keyframes nodeAppear {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.recipe-step-node-content {
  animation: nodeAppear 0.3s ease-out;
}
</style>
