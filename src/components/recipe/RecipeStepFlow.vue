<template>
  <div id="recipe-step-flow" :style="{ width: '100%', height: modeConfig.height }">
    <!-- Loading 状态 -->
    <div v-if="loading" class="flow-loading">
      <a-spin size="large" tip="加载中..." />
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="flow-error">
      <a-result status="error" :title="error" sub-title="请稍后重试">
        <template #extra>
          <a-button type="primary" @click="handleRetry">重新加载</a-button>
        </template>
      </a-result>
    </div>

    <!-- 正常流程图 -->
    <VueFlow
      v-else
      v-model="elements"
      :nodes="nodes"
      :edges="edges"
      @node-drag-stop="onNodeDragStop"
      @node-click="onNodeClick"
      :fit-view-on-init="modeConfig.fitViewOnInit"
      :min-zoom="modeConfig.minZoom"
      :max-zoom="modeConfig.maxZoom"
      :nodes-draggable="isEditable"
      :nodes-connectable="false"
      :elements-selectable="true"
      :auto-pan="false"
      :pan-on-scroll="true"
      :zoom-on-scroll="true"
      :zoom-on-pinch="true"
      :zoom-on-double-click="false"
      :style="{ width: '100%', height: modeConfig.height }"
    >
      <!-- 自定义节点模板 -->
      <template #node-recipeStep="nodeProps">
        <RecipeStepNode
          v-bind="nodeProps"
          :mode="props.mode"
          :editable="isEditable"
          @update="handleNodeUpdate"
          @click="handleNodeClickWrapper"
        />
      </template>
    </VueFlow>

    <!-- 工具栏（可选） -->
    <div v-if="shouldShowToolbar" class="flow-toolbar">
      <a-space>
        <a-button @click="autoLayout" :loading="layouting">
          <template #icon><ApartmentOutlined /></template>
          自动布局
        </a-button>
        <a-button @click="fitView">
          <template #icon><ExpandOutlined /></template>
          适应视图
        </a-button>
        <a-button @click="resetZoom">
          <template #icon><ReloadOutlined /></template>
          重置缩放
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script lang="ts" setup>
/* these are necessary styles for vue flow */
import '@vue-flow/core/dist/style.css'
import { VueFlow, useVueFlow } from '@vue-flow/core'

/* this contains the default theme, these are optional styles */
import '@vue-flow/core/dist/theme-default.css'
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { ApartmentOutlined, ExpandOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import {
  convertRecipeStepsToNodes,
  convertNodesToRecipeSteps,
  generateStepEdges,
  autoLayoutNodes,
  batchUpdatePositionsByOrderIndex,
  debugStepsData,
} from '../../utils/recipeStepConverter.js'
import RecipeStepNode from './RecipeStepNode.vue'

// Props 接口定义
interface Props {
  recipeList: any[] // 修正拼写错误 recipeLsit -> recipeList
  showToolbar?: boolean
  editable?: boolean
  autoSave?: boolean
  mode?: 'full' | 'preview' | 'compact' // 新增模式
  height?: string // 允许自定义高度
}

const props = withDefaults(defineProps<Props>(), {
  recipeList: () => [],
  showToolbar: true,
  editable: false,
  autoSave: true,
  mode: 'full',
  height: '500px',
})

// Emits 定义
const emit = defineEmits<{
  nodeClick: [node: any]
  nodeUpdate: [node: any]
  positionChange: [nodes: any[]]
  error: [error: string]
}>()

// 响应式数据
const loading = ref(false)
const error = ref('')
const layouting = ref(false)
const nodes = ref<any[]>([])
const edges = ref<any[]>([])
const elements = ref<any[]>([])

// Vue Flow 实例
const { fitView, zoomTo, setViewport } = useVueFlow()

// 计算属性
const hasValidData = computed(() => {
  return Array.isArray(props.recipeList) && props.recipeList.length > 0
})

// 根据模式计算配置
const modeConfig = computed(() => {
  switch (props.mode) {
    case 'preview':
      return {
        nodeSpacing: { x: 180, y: 120 },
        showToolbar: false,
        editable: false,
        fitViewOnInit: true,
        minZoom: 0.5,
        maxZoom: 2,
        height: props.height || '250px',
      }
    case 'compact':
      return {
        nodeSpacing: { x: 200, y: 140 },
        showToolbar: false,
        editable: props.editable,
        fitViewOnInit: true,
        minZoom: 0.3,
        maxZoom: 3,
        height: props.height || '350px',
      }
    default: // 'full'
      return {
        nodeSpacing: { x: 250, y: 180 },
        showToolbar: props.showToolbar,
        editable: props.editable,
        fitViewOnInit: true,
        minZoom: 0.1,
        maxZoom: 4,
        height: props.height || '500px',
      }
  }
})

// 是否显示工具栏
const shouldShowToolbar = computed(() => {
  return modeConfig.value.showToolbar && hasValidData.value
})

// 是否可编辑
const isEditable = computed(() => {
  return modeConfig.value.editable && hasValidData.value
})

// 初始化数据
const initializeData = async (shouldFitView: boolean = true) => {
  if (!hasValidData.value) {
    nodes.value = []
    edges.value = []
    elements.value = []
    return
  }

  try {
    loading.value = true
    error.value = ''

    // 调试数据
    debugStepsData(props.recipeList)

    // 转换数据
    const convertedNodes = convertRecipeStepsToNodes(props.recipeList, {
      nodeType: 'recipeStep',
      defaultSpacing: modeConfig.value.nodeSpacing,
    })

    const convertedEdges = generateStepEdges(convertedNodes)

    nodes.value = convertedNodes
    edges.value = convertedEdges
    elements.value = [...convertedNodes, ...convertedEdges]

    // 只在初始加载时适应视图，而不是每次数据更新时
    if (shouldFitView) {
      await nextTick()
      setTimeout(() => {
        fitView()
      }, 100)
    }
  } catch (err) {
    const errorMsg = err instanceof Error ? err.message : '数据加载失败'
    error.value = errorMsg
    emit('error', errorMsg)
    console.error('RecipeStepFlow initialization failed:', err)
  } finally {
    loading.value = false
  }
}

// 节点拖拽结束事件
const onNodeDragStop = async (event: any) => {
  if (!isEditable.value) return

  try {
    const { node } = event
    console.log('Node drag stopped:', node)

    // 立即更新本地nodes数组中对应节点的位置
    const nodeIndex = nodes.value.findIndex((n) => n.id === node.id)
    if (nodeIndex !== -1) {
      nodes.value[nodeIndex] = {
        ...nodes.value[nodeIndex],
        position: { ...node.position },
      }

      // 同时更新elements数组
      const elementIndex = elements.value.findIndex((el) => el.id === node.id)
      if (elementIndex !== -1) {
        elements.value[elementIndex] = {
          ...elements.value[elementIndex],
          position: { ...node.position },
        }
      }
    }

    if (props.autoSave) {
      // 发送位置变化事件，让父组件更新store
      emit('positionChange', nodes.value)
    }
  } catch (err) {
    console.error('Failed to save node position:', err)
    message.error('保存位置失败')
  }
}

// 这些事件处理函数已移除，以防止不必要的重新渲染和视图重置
// onNodesChange 和 onEdgesChange 会在每次微小变化时触发，导致性能问题

// 节点点击事件（VueFlow 原生）
const onNodeClick = (event: any) => {
  const { node } = event
  console.log('VueFlow node clicked:', node)
  emit('nodeClick', node)
}

// 自定义节点内部点击事件
const handleNodeClick = (nodeProps: any, data: any) => {
  console.log('Custom node clicked:', data)
  console.log('Node props:', nodeProps)

  // 发送完整的节点信息
  const nodeInfo = {
    ...nodeProps,
    data: data,
    clickSource: 'custom', // 标记来源
  }

  emit('nodeClick', nodeInfo)
}

// 包装函数来处理节点点击事件
const handleNodeClickWrapper = (data: any) => {
  // 找到对应的节点props
  const nodeProps = nodes.value.find((node) => node.data.orderIndex === data.orderIndex)
  if (nodeProps) {
    handleNodeClick(nodeProps, data)
  }
}

// 节点更新事件
const handleNodeUpdate = (updatedNode: any) => {
  const index = nodes.value.findIndex((n) => n.id === updatedNode.id)
  if (index !== -1) {
    nodes.value[index] = { ...nodes.value[index], ...updatedNode }
    emit('nodeUpdate', updatedNode)
  }
}

// 自动布局
const autoLayout = async () => {
  if (!hasValidData.value) return

  try {
    layouting.value = true
    const layoutedNodes = autoLayoutNodes(nodes.value, {
      type: 'flow',
      spacing: { x: 280, y: 200 },
    })

    nodes.value = layoutedNodes
    elements.value = [...layoutedNodes, ...edges.value]

    await nextTick()
    setTimeout(() => {
      fitView()
    }, 100)

    if (props.autoSave) {
      emit('positionChange', nodes.value)
    }

    message.success('自动布局完成')
  } catch (err) {
    console.error('Auto layout failed:', err)
    message.error('自动布局失败')
  } finally {
    layouting.value = false
  }
}

// 重置缩放
const resetZoom = () => {
  zoomTo(1)
  setViewport({ x: 0, y: 0, zoom: 1 })
}

// 重试加载
const handleRetry = () => {
  initializeData()
}

// 监听数据变化 - 智能判断是位置更新还是结构变化
watch(
  () => props.recipeList,
  (newList, oldList) => {
    // 如果这只是位置更新（相同数量的步骤，相同的ID），
    // 使用 updateNodeData() 而不是完全重新初始化
    if (
      oldList &&
      newList &&
      oldList.length === newList.length &&
      oldList.every((step, index) => {
        const newStep = newList[index]
        return (
          newStep && step.recipeId === newStep.recipeId && step.orderIndex === newStep.orderIndex
        )
      })
    ) {
      console.log('🔄 检测到位置更新，使用 updateNodeData() 避免自动缩放')
      updateNodeData()
    } else {
      // 这是结构性变化，所以用 fitView 重新初始化
      console.log('🔄 检测到结构变化，使用 initializeData() 并自动适应视图')
      initializeData()
    }
  },
  { deep: true, immediate: true },
)

// 组件挂载时初始化
onMounted(() => {
  initializeData()
})

// 更新节点数据而不重置视图
const updateNodeData = async () => {
  if (!hasValidData.value) return

  try {
    console.log('🔄 更新节点数据而不重置视图')
    console.log('🔍 当前 props.recipeList:', props.recipeList)

    // 转换数据但保持当前视图状态
    const convertedNodes = convertRecipeStepsToNodes(props.recipeList, {
      nodeType: 'recipeStep',
      defaultSpacing: modeConfig.value.nodeSpacing,
    })

    const convertedEdges = generateStepEdges(convertedNodes)

    // 保持现有节点的位置信息（如果存在）
    const updatedNodes = convertedNodes.map((newNode) => {
      const existingNode = nodes.value.find((n) => n.id === newNode.id)
      if (existingNode) {
        console.log(`🔄 更新节点 ${newNode.id}:`, {
          oldData: existingNode.data,
          newData: newNode.data,
          position: existingNode.position,
        })

        // 保持现有位置，但更新数据
        return {
          ...newNode,
          position: existingNode.position, // 保持当前位置
          data: {
            ...newNode.data,
            // 确保使用最新的数据
          },
        }
      }
      return newNode
    })

    nodes.value = updatedNodes
    edges.value = convertedEdges
    elements.value = [...updatedNodes, ...convertedEdges]

    console.log('✅ 节点数据更新完成，视图保持不变')
    console.log('🔍 更新后的 nodes:', nodes.value)
  } catch (err) {
    console.error('更新节点数据失败:', err)
  }
}

// 暴露方法给父组件
defineExpose({
  autoLayout,
  fitView,
  resetZoom,
  initializeData,
  updateNodeData,
})
</script>

<style scoped>
/* import the necessary styles for Vue Flow to work */
@import '@vue-flow/core/dist/style.css';

/* import the default theme, this is optional but generally recommended */
@import '@vue-flow/core/dist/theme-default.css';

#recipe-step-flow {
  width: 100%;
  /* height 通过内联样式设置，确保Vue Flow能正确识别 */
  position: relative;
  background: #fafafa;
  border-radius: 8px;
  overflow: hidden;
  transition: height 0.3s ease;
}

/* Loading 状态 */
.flow-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #fafafa;
  border-radius: 8px;
}

/* 错误状态 */
.flow-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #fafafa;
  border-radius: 8px;
}

/* 工具栏样式 */
.flow-toolbar {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Vue Flow 自定义样式 */
:deep(.vue-flow__node) {
  cursor: pointer;
}

/* 禁用Vue Flow默认选择样式，让组件自己处理选择状态 */
:deep(.vue-flow__node.selected) {
  box-shadow: none !important;
  /* 让RecipeStepNode组件完全控制选择样式 */
}

:deep(.vue-flow__edge) {
  cursor: pointer;
}

:deep(.vue-flow__edge.selected .vue-flow__edge-path) {
  stroke: #ff7a00 !important;
  stroke-width: 3px !important;
}

/* 控制按钮样式 */
:deep(.vue-flow__controls button) {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e6e6e6;
  color: #666;
}

:deep(.vue-flow__controls button:hover) {
  background: #ff7a00;
  border-color: #ff7a00;
  color: white;
}

/* 小地图样式 */
:deep(.vue-flow__minimap) {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e6e6e6;
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  #recipe-step-flow {
    height: 400px;
  }

  .flow-toolbar {
    top: 8px;
    right: 8px;
    padding: 6px;
  }

  .flow-toolbar :deep(.ant-btn) {
    font-size: 12px;
    height: 32px;
    min-width: 32px;
  }
}

/* 橙色系主题配色 */
:deep(.vue-flow__background) {
  background-color: #fffbf7; /* 极淡的橙色背景 */
}

:deep(.vue-flow__background .vue-flow__background-pattern) {
  fill: rgba(255, 122, 0, 0.1); /* 淡橙色网格 */
}
</style>
