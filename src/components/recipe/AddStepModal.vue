<template>
  <a-modal
    :open="visible"
    title="添加新步骤"
    :width="600"
    :mask-closable="false"
    class="add-step-modal"
    @ok="handleCreateStep"
    @cancel="handleCancel"
    :confirm-loading="loading"
    ok-text="创建步骤"
    cancel-text="取消"
  >
    <div class="add-step-form">
      <!-- 基本信息 -->
      <div class="form-section">
        <h4 class="section-header">
          <InfoCircleOutlined />
          步骤信息
        </h4>
        <div class="form-field">
          <label>步骤标题 <span class="required">*</span></label>
          <a-input
            v-model:value="formData.title"
            placeholder="请输入步骤标题"
            :maxlength="50"
            show-count
          />
        </div>
        <div class="form-field">
          <label>步骤备注</label>
          <a-textarea
            v-model:value="formData.note"
            placeholder="请输入步骤详细说明"
            :rows="3"
            :maxlength="200"
            show-count
          />
        </div>
        <div class="form-row">
          <div class="form-field">
            <label>预计时间 (分钟)</label>
            <a-input-number
              v-model:value="formData.estMinutes"
              :min="0"
              :max="999"
              placeholder="预计耗时"
              style="width: 100%"
            />
          </div>
          <div class="form-field">
            <label>并行设置</label>
            <a-switch
              v-model:value="formData.parallelizable"
              checked-children="可并行"
              un-checked-children="顺序执行"
            />
          </div>
        </div>
      </div>

      <!-- 位置设置 -->
      <div class="form-section">
        <h4 class="section-header">
          <NodeIndexOutlined />
          插入位置
        </h4>
        <div class="form-field">
          <label>选择插入位置</label>
          <a-select
            v-model:value="formData.insertPosition"
            placeholder="选择在哪里插入新步骤"
            style="width: 100%"
          >
            <a-select-option value="start">在开头 (第1步)</a-select-option>
            <a-select-option 
              v-for="(step, index) in existingSteps" 
              :key="index"
              :value="index + 1"
            >
              在 "{{ step.title || `步骤${index + 1}` }}" 后 (第{{ index + 2 }}步)
            </a-select-option>
            <a-select-option value="end">在末尾 (第{{ existingSteps.length + 1 }}步)</a-select-option>
          </a-select>
        </div>
        <div class="position-preview">
          <span class="preview-text">
            新步骤将成为第 {{ getNewStepOrderIndex() + 1 }} 步
          </span>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { 
  InfoCircleOutlined,
  NodeIndexOutlined
} from '@ant-design/icons-vue'

// Props 定义
interface Props {
  visible: boolean
  existingSteps: any[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  existingSteps: () => [],
  loading: false
})

// Emits 定义
const emit = defineEmits<{
  close: []
  create: [stepData: any]
}>()

// 表单数据
const formData = ref({
  title: '',
  note: '',
  estMinutes: null,
  parallelizable: false,
  insertPosition: 'end'
})

// 重置表单
const resetForm = () => {
  formData.value = {
    title: '',
    note: '',
    estMinutes: null,
    parallelizable: false,
    insertPosition: 'end'
  }
}

// 计算新步骤的orderIndex
const getNewStepOrderIndex = () => {
  const position = formData.value.insertPosition
  
  if (position === 'start') {
    return 0
  } else if (position === 'end') {
    return props.existingSteps.length
  } else {
    // position 是数字，表示插入到某个位置之后
    return Number(position)
  }
}

// 计算新步骤的位置坐标
const calculateNewStepPosition = (orderIndex: number) => {
  const spacing = { x: 250, y: 180 }
  const start = { x: 100, y: 100 }
  
  // 使用网格布局：3列排列
  const x = start.x + (orderIndex % 3) * spacing.x
  const y = start.y + Math.floor(orderIndex / 3) * spacing.y
  
  return { x, y }
}

// 处理创建步骤
const handleCreateStep = () => {
  // 验证表单
  console.log('Form data before validation:', formData.value) // 调试信息
  console.log('Title value:', `"${formData.value.title}"`) // 调试标题具体值
  
  if (!formData.value.title || formData.value.title.trim() === '') {
    console.error('Title validation failed:', formData.value.title) // 调试信息
    message.error('请输入步骤标题')
    return
  }
  
  if (formData.value.estMinutes !== null && formData.value.estMinutes < 0) {
    message.error('预计时间不能为负数')
    return
  }
  
  // 计算插入位置
  const newOrderIndex = getNewStepOrderIndex()
  const newPosition = calculateNewStepPosition(newOrderIndex)
  
  // 构建新步骤数据
  const stepData = {
    title: formData.value.title.trim(),
    note: formData.value.note || '',
    orderIndex: newOrderIndex,
    estMinutes: formData.value.estMinutes || null,
    parallelizable: formData.value.parallelizable ? 1 : 0,
    positionX: newPosition.x,
    positionY: newPosition.y,
    recipeId: props.existingSteps[0]?.recipeId || null,
    createTime: new Date().toISOString(),
    updateTime: new Date().toISOString()
  }
  
  console.log('Emitting step data:', stepData) // 调试信息
  emit('create', stepData)
}

// 处理取消
const handleCancel = () => {
  resetForm()
  emit('close')
}

// 监听 visible 变化，重置表单
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    resetForm()
  }
})
</script>

<style scoped>
/* 添加步骤弹窗样式 */
.add-step-modal :deep(.ant-modal-header) {
  background: linear-gradient(135deg, #fff7ed 0%, #ffedd5 100%);
  border-bottom: 1px solid #fed7aa;
}

.add-step-modal :deep(.ant-modal-footer) {
  border-top: 1px solid #fed7aa;
  background: #fafafa;
}

.add-step-modal :deep(.ant-btn-primary) {
  background: #ff7a00;
  border-color: #ff7a00;
}

.add-step-modal :deep(.ant-btn-primary:hover) {
  background: #e66d00;
  border-color: #e66d00;
}

/* 添加步骤表单样式 */
.add-step-form {
  padding: 8px 0;
}

.form-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-header {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #dee2e6;
}

.form-field {
  margin-bottom: 16px;
}

.form-field:last-child {
  margin-bottom: 0;
}

.form-field label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.required {
  color: #dc3545;
  margin-left: 4px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-row .form-field {
  margin-bottom: 0;
}

.add-step-form .ant-input,
.add-step-form .ant-input-number,
.add-step-form .ant-textarea,
.add-step-form .ant-select {
  border-radius: 6px;
  border: 1px solid #ced4da;
  transition: all 0.2s ease;
}

.add-step-form .ant-input:focus,
.add-step-form .ant-input-number:focus,
.add-step-form .ant-textarea:focus,
.add-step-form .ant-select:focus {
  border-color: #ff7a00;
  box-shadow: 0 0 0 2px rgba(255, 122, 0, 0.1);
}

.add-step-form .ant-switch {
  background-color: #6c757d;
}

.add-step-form .ant-switch-checked {
  background-color: #ff7a00;
}

/* 位置预览 */
.position-preview {
  margin-top: 12px;
  padding: 8px 12px;
  background: #e7f3ff;
  border-radius: 6px;
  border: 1px solid #bee5eb;
}

.preview-text {
  font-size: 13px;
  color: #0c5460;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .add-step-modal :deep(.ant-modal) {
    margin: 0;
    max-width: 100%;
    top: 0;
    padding-bottom: 0;
  }
  
  .add-step-modal :deep(.ant-modal-content) {
    border-radius: 0;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .form-section {
    padding: 12px;
  }
  
  .section-header {
    font-size: 14px;
  }
}
</style>
