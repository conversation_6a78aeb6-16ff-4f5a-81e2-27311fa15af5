<!-- src/components/recipe/IngredientDetailModal.vue -->
<template>
  <a-modal
    :open="visible"
    :title="modalTitle"
    :footer="null"
    :closable="true"
    @cancel="handleClose"
    :width="600"
    :mask-closable="false"
    class="ingredient-detail-modal"
  >
    <div class="modal-content">
      <!-- 编辑模式 -->
      <div v-if="mode === 'edit'" class="edit-mode">
        <a-form :model="editableIngredients" layout="vertical" class="ingredient-form">
          <div class="ingredient-list-edit">
            <div
              v-for="(ingredient, index) in editableIngredients"
              :key="ingredient.key || index"
              class="ingredient-item-edit"
            >
              <a-row :gutter="16" align="middle">
                <a-col :span="10">
                  <a-form-item
                    :label="index === 0 ? '食材名称' : ''"
                    :name="['ingredients', index, 'name']"
                  >
                    <a-input
                      v-model:value="ingredient.name"
                      placeholder="请输入食材名称"
                      :disabled="!isEditable"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item
                    :label="index === 0 ? '用量' : ''"
                    :name="['ingredients', index, 'amount']"
                  >
                    <a-input
                      v-model:value="ingredient.amount"
                      placeholder="请输入用量"
                      :disabled="!isEditable"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item :label="index === 0 ? '操作' : ''">
                    <a-space>
                      <a-button
                        v-if="isEditable"
                        type="text"
                        danger
                        :icon="h(DeleteOutlined)"
                        @click="removeIngredient(index)"
                        size="small"
                      />
                    </a-space>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </div>

          <a-button
            v-if="isEditable"
            type="dashed"
            block
            @click="addIngredient"
            class="add-ingredient-btn"
          >
            <PlusOutlined /> 添加食材
          </a-button>
        </a-form>
      </div>

      <!-- 查看模式 -->
      <div v-else class="view-mode">
        <a-table
          :columns="viewColumns"
          :data-source="ingredients"
          :pagination="false"
          size="middle"
          :scroll="{ y: 400 }"
          class="ingredients-table"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <span class="ingredient-name">{{ record.name }}</span>
            </template>
            <template v-if="column.key === 'amount'">
              <span class="ingredient-amount">{{ record.amount }}</span>
            </template>
          </template>
        </a-table>
      </div>

      <!-- 底部操作按钮 -->
      <div class="modal-footer">
        <a-space>
          <a-button @click="handleClose">
            {{ mode === 'edit' ? '取消' : '关闭' }}
          </a-button>
          <a-button
            v-if="mode === 'edit' && isEditable"
            type="primary"
            @click="handleSave"
            :loading="saving"
          >
            保存
          </a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, h } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue'

// 定义接口
interface Ingredient {
  key?: string
  name: string
  amount: string
}

interface Props {
  visible: boolean
  mode: 'edit' | 'view' // 编辑模式或查看模式
  ingredients: Ingredient[]
  title?: string
  editable?: boolean // 是否允许编辑（即使在edit模式下也可以设为false来只读）
}

// Props定义
const props = withDefaults(defineProps<Props>(), {
  visible: false,
  mode: 'view',
  ingredients: () => [],
  title: '',
  editable: true,
})

// Emits定义
const emit = defineEmits<{
  close: []
  save: [ingredients: Ingredient[]]
}>()

// 响应式数据
const editableIngredients = ref<Ingredient[]>([])
const saving = ref(false)

// 计算属性
const modalTitle = computed(() => {
  if (props.title) return props.title
  return props.mode === 'edit' ? '编辑食材清单' : '查看食材清单'
})

const isEditable = computed(() => {
  return props.mode === 'edit' && props.editable
})

// 查看模式的表格列配置
const viewColumns = [
  {
    title: '食材名称',
    dataIndex: 'name',
    key: 'name',
    width: '60%',
  },
  {
    title: '用量',
    dataIndex: 'amount',
    key: 'amount',
    width: '40%',
  },
]

// 监听props变化，初始化编辑数据
watch(
  () => [props.visible, props.ingredients],
  () => {
    if (props.visible) {
      editableIngredients.value = props.ingredients.map((item, index) => ({
        ...item,
        key: item.key || `ingredient-${index}`,
      }))
    }
  },
  { immediate: true, deep: true },
)

// 方法
const handleClose = () => {
  emit('close')
}

const handleSave = async () => {
  try {
    saving.value = true

    // 验证数据
    const validIngredients = editableIngredients.value.filter(
      (item) => item.name.trim() && item.amount.trim(),
    )

    if (validIngredients.length === 0) {
      message.warning('请至少添加一个有效的食材')
      return
    }

    // 发送保存事件
    emit('save', validIngredients)
    message.success('保存成功')
  } catch (error) {
    message.error('保存失败')
  } finally {
    saving.value = false
  }
}

const addIngredient = () => {
  editableIngredients.value.push({
    key: `ingredient-${Date.now()}`,
    name: '',
    amount: '',
  })
}

const removeIngredient = (index: number) => {
  editableIngredients.value.splice(index, 1)
}
</script>

<style lang="less" scoped>
.ingredient-detail-modal :deep(.ant-modal-body) {
  padding: 24px;
}

.modal-content {
  min-height: 300px;
}

.edit-mode {
  .ingredient-form {
    margin-bottom: 24px;
  }

  .ingredient-list-edit {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 16px;
  }

  .ingredient-item-edit {
    margin-bottom: 8px;
    padding: 8px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    background: #fafafa;

    &:hover {
      border-color: #ff7a00;
      background: #fff9f0;
    }
  }

  .add-ingredient-btn {
    border-color: #ff7a00;
    color: #ff7a00;

    &:hover {
      border-color: #e66d00;
      color: #e66d00;
    }
  }
}

.view-mode {
  .ingredients-table {
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    overflow: hidden;
  }

  .ingredients-table :deep(.ant-table-thead > tr > th) {
    background-color: #fafafa;
    color: #6b6b6b;
    font-weight: 500;
    border-bottom: 1px solid #f0f0f0;
    text-align: center;
  }

  .ingredients-table :deep(.ant-table-tbody > tr > td) {
    border-bottom: 1px solid #f5f5f5;
    padding: 12px 16px;
  }

  .ingredients-table :deep(.ant-table-tbody > tr:hover > td) {
    background-color: #fff9f0;
  }

  .ingredient-name {
    font-weight: 500;
    color: #4a4a4a;
  }

  .ingredient-amount {
    color: #7a7a7a;
    font-weight: 500;
  }
}

.modal-footer {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ingredient-detail-modal :deep(.ant-modal) {
    width: 90% !important;
    margin: 20px auto;
  }

  .edit-mode .ingredient-item-edit .ant-col {
    margin-bottom: 8px;
  }
}
</style>
