<template>
  <div id="global-sider">
    <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      width="256"
      class="app-sider"
    >
      <div class="logo">
        <AppLogo
          :size="collapsed ? 'small' : 'medium'"
          variant="colored"
          :showText="!collapsed"
          text="Recipe Hub"
        />
      </div>

      <a-menu
        v-model:selectedKeys="selectedKeys"
        :mode="'inline'"
        theme="light"
        class="app-menu"
        :items="originItems"
        @click="doMenuClick"
      >
      </a-menu>
    </a-layout-sider>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { type MenuProps, message } from 'ant-design-vue'

import { AppLogo, UserAvatar } from '@/components'

/**
 * 应用主布局组件
 * 包含侧边栏导航、顶部工具栏和主内容区域
 */

const route = useRoute()
const router = useRouter()
//lable数组
const originItems: MenuProps['items'] = [
  {
    key: '/dashboard',
    label: '仪表板',
    title: '仪表板',
  },
  {
    key: '/recipes',
    label: '食谱浏览',
    title: '食谱浏览',
  },
  {
    key: '/recipegallery',
    label: '我的食谱',
    title: '我的食谱',
  },
  {
    key: '/favorites',
    label: '收藏夹',
    title: '收藏夹',
  },
  {
    key: '/grocery',
    label: '购物清单',
    title: '购物清单',
  },
]
// 响应式状态
const collapsed = ref(false)
const selectedKeys = ref<string[]>([])
router.afterEach((to) => {
  selectedKeys.value = [to.path]
})

const doMenuClick = ({ key }: { key: string }) => {
  router.push({
    path: key,
  })
}

// 监听路由变化，更新选中的菜单项
// watch(
//   () => route.path,
//   (newPath) => {
//     if (newPath.includes('/dashboard')) {
//       selectedKeys.value = ['dashboard']
//     } else if (newPath.includes('/recipes')) {
//       selectedKeys.value = ['recipes']
//     } else if (newPath.includes('/collections')) {
//       selectedKeys.value = ['collections']
//     } else if (newPath.includes('/favorites')) {
//       selectedKeys.value = ['favorites']
//     } else if (newPath.includes('/grocery')) {
//       selectedKeys.value = ['grocery']
//     } else if (newPath.includes('/profile')) {
//       selectedKeys.value = ['profile']
//     }
//   },
//   { immediate: true }
// )

// 事件处理
const handleLogout = async () => {
  try {
    // TODO: 调用登出API
    // await userStore.logout()
    message.success('退出登录成功')
    await router.push('/user/login')
  } catch (error) {
    message.error('退出登录失败')
  }
}
</script>

<style lang="less" scoped>
.app-sider {
  background: #fff;
  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);

  .logo {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .app-menu {
    border-right: none;
    padding: 16px 0;

    :deep(.ant-menu-item) {
      margin: 4px 12px;
      border-radius: 8px;

      i {
        margin-right: 8px;
        width: 16px;
        text-align: center;
      }

      &:hover,
      &.ant-menu-item-selected {
        background-color: #fff2e8;
        color: #ff7a00;
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .app-sider {
    position: fixed;
    z-index: 1000;
    height: 100vh;
  }
}
</style>
