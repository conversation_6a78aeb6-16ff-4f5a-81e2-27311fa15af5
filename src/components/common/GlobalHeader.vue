<template>
  <div id="global-header">
    <a-layout-header class="app-header">
      <div class="header-left">
        <a-button
          type="text"
          :icon="collapsed ? h(MenuUnfoldOutlined) : h(MenuFoldOutlined)"
          @click="collapsed = !collapsed"
          class="trigger"
        />
      </div>

      <div class="header-right">
        <a-space :size="16">
          <a-button type="text" :icon="h(BellOutlined)" />
          <a-button type="text" :icon="h(SettingOutlined)" />

          <a-dropdown>
            <UserAvatar
              :src="userAvatar"
              :userName="userName"
              :userRole="userRole"
              size="medium"
              :clickable="true"
              :showOnlineStatus="true"
              :isOnline="true"
            />
            <template #overlay>
              <a-menu>
                <a-menu-item @click="$router.push('/profile')">
                  <i class="fa fa-user"></i>
                  个人资料
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item @click="handleLogout">
                  <i class="fa fa-sign-out"></i>
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </div>
    </a-layout-header>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BellOutlined,
  SettingOutlined,
} from '@ant-design/icons-vue'
import { useLoginUserStore } from '@/stores/userLoginStore'
import { UserAvatar } from '@/components'
/**
 * 应用主布局组件
 * 包含侧边栏导航、顶部工具栏和主内容区域
 */

const route = useRoute()
const router = useRouter()
const userStore = useLoginUserStore()

// 响应式状态
const collapsed = ref(false)
const selectedKeys = ref<string[]>([])

// 计算属性
const userName = computed(() => userStore.loginUser?.userName)
const userAvatar = computed(() => userStore.loginUser?.userAvatar)
const userRole = computed(() => userStore.loginUser?.userRole)

// 监听路由变化，更新选中的菜单项
watch(
  () => route.path,
  (newPath) => {
    if (newPath.includes('/dashboard')) {
      selectedKeys.value = ['dashboard']
    } else if (newPath.includes('/recipes')) {
      selectedKeys.value = ['recipes']
    } else if (newPath.includes('/collections')) {
      selectedKeys.value = ['collections']
    } else if (newPath.includes('/favorites')) {
      selectedKeys.value = ['favorites']
    } else if (newPath.includes('/grocery')) {
      selectedKeys.value = ['grocery']
    } else if (newPath.includes('/profile')) {
      selectedKeys.value = ['profile']
    }
  },
  { immediate: true },
)

// 事件处理
const handleLogout = async () => {
  try {
    // TODO: 调用登出API
    // await userStore.logout()
    message.success('退出登录成功')
    await router.push('/user/login')
  } catch (error) {
    message.error('退出登录失败')
  }
}
</script>

<style lang="less" scoped>
.app-header {
  background: #fff;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  z-index: 10;

  .header-left {
    .trigger {
      font-size: 18px;
      line-height: 64px;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: #ff7a00;
      }
    }
  }

  .header-right {
    :deep(.ant-btn) {
      border: none;
      box-shadow: none;

      &:hover {
        color: #ff7a00;
        background-color: #fff2e8;
      }
    }
  }
}

// 响应式适配
</style>
