// 临时 API 类型定义文件
declare namespace API {
  interface BaseResponse<T = any> {
    code: number;
    data?: T;
    message?: string;
  }

  interface RecipeStepVO {
    id?: number;
    recipeId?: number;
    title?: string;
    note?: string;
    orderIndex?: number;
    estMinutes?: number;
    parallelizable?: number;
    positionX?: number;
    positionY?: number;
    createTime?: string;
    updateTime?: string;
  }

  interface RecipeVO {
    id?: number;
    userId?: number;
    name?: string;
    category?: string;
    tags?: string;
    introduction?: string;
    coverKey?: string;
    coverStatus?: string;
    stepCount?: number;
    steps?: RecipeStepVO[];
    createTime?: string;
    updateTime?: string;
  }

  interface RecipeDetailVO extends RecipeVO {
    steps?: RecipeStepVO[];
  }

  interface DeleteRequest {
    id: number;
  }

  interface RecipeQueryRequest {
    current?: number;
    pageSize?: number;
    sortField?: string;
    sortOrder?: string;
    name?: string;
    category?: string;
    userId?: number;
  }

  interface RecipeAddRequest {
    name: string;
    category?: string;
    tags?: string;
    introduction?: string;
    coverKey?: string;
  }

  interface RecipeUpdateRequest {
    id: number;
    name?: string;
    category?: string;
    tags?: string;
    introduction?: string;
    coverKey?: string;
  }

  interface RecipeStepBulkSaveRequest {
    steps: RecipeStepVO[];
  }

  type BaseResponseListRecipeStepVO_ = BaseResponse<RecipeStepVO[]>;
  type BaseResponseRecipeVO_ = BaseResponse<RecipeVO>;
  type BaseResponseRecipeDetailVO_ = BaseResponse<RecipeDetailVO>;
  type BaseResponsePageRecipeVO_ = BaseResponse<{
    records: RecipeVO[];
    total: number;
    size: number;
    current: number;
    pages: number;
  }>;
  type BaseResponseBoolean_ = BaseResponse<boolean>;

  interface getRecipeStepsUsingGETParams {
    recipeId: number;
  }

  interface saveRecipeStepsUsingPOSTParams {
    recipeId: number;
  }

  interface getRecipeUsingGETParams {
    id: number;
  }
}
